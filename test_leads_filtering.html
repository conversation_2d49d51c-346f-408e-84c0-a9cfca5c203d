<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Leads Filtering Test</title>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #121212;
            color: #ffffff;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: #1e1e1e;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        h1 {
            color: #e91e63;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            background: #2a2a2a;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #e91e63;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 6px;
            border-left: 4px solid;
        }
        .test-result.success {
            background: rgba(76, 175, 80, 0.1);
            border-color: #4caf50;
            color: #4caf50;
        }
        .test-result.error {
            background: rgba(244, 67, 54, 0.1);
            border-color: #f44336;
            color: #f44336;
        }
        .test-result.info {
            background: rgba(33, 150, 243, 0.1);
            border-color: #2196f3;
            color: #2196f3;
        }
        button {
            background: linear-gradient(135deg, #e91e63, #ff5722);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            margin: 10px 10px 10px 0;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .metric-card {
            background: #333;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #e91e63;
        }
        .metric-label {
            font-size: 14px;
            color: #aaa;
            margin-top: 5px;
        }
        .filter-controls {
            margin: 20px 0;
        }
        .filter-controls select {
            background: #333;
            color: white;
            border: 1px solid #555;
            padding: 8px 12px;
            border-radius: 4px;
            margin: 0 10px;
        }
        .date-range-info {
            background: #333;
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Leads Filtering Test - Latest Available Data Approach</h1>
        <p>This page tests the new "latest available data" filtering approach for the Leads Report, similar to Google Ads.</p>

        <div class="test-section">
            <h2>📊 Data Analysis</h2>
            <button onclick="analyzeLeadData()">Analyze Lead Data</button>
            <div id="data-analysis-results"></div>
            <div class="metrics-grid" id="data-metrics"></div>
        </div>

        <div class="test-section">
            <h2>🔍 Date Filtering Test</h2>
            <div class="filter-controls">
                <label>Test Filter:</label>
                <select id="test-filter">
                    <option value="all">All Time</option>
                    <option value="last-14">Last 14 Days</option>
                    <option value="last-30">Last 30 Days</option>
                    <option value="last-60">Last 60 Days</option>
                    <option value="last-90">Last 90 Days</option>
                </select>
                <button onclick="testDateFilter()">Apply Filter</button>
            </div>
            <div id="filter-results"></div>
            <div class="date-range-info" id="date-range-display"></div>
        </div>

        <div class="test-section">
            <h2>📈 Traffic Source Breakdown</h2>
            <button onclick="analyzeTrafficSources()">Analyze Traffic Sources</button>
            <div id="traffic-source-results"></div>
            <div class="metrics-grid" id="traffic-metrics"></div>
        </div>
    </div>

    <script>
        let testLeadData = [];
        let testDateRange = null;

        function logResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            container.appendChild(resultDiv);
        }

        async function analyzeLeadData() {
            logResult('data-analysis-results', 'Loading Lead data from Airtable...', 'info');
            
            try {
                // Fetch data from your Airtable API endpoint
                const response = await fetch('/api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblcdFVUC3zJrbmNf&maxRecords=2000');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                testLeadData = Array.isArray(data) ? data : (data.records || []);
                
                logResult('data-analysis-results', `✅ Successfully loaded ${testLeadData.length} Lead records`, 'success');
                
                // Analyze date range
                const dates = testLeadData.map(record => record['Date Created']).filter(Boolean);
                const parsedDates = dates.map(dateStr => {
                    if (dateStr.match(/^\d{4}-\d{2}-\d{2}$/)) {
                        return new Date(dateStr + 'T00:00:00');
                    }
                    return new Date(dateStr);
                }).filter(date => !isNaN(date));

                if (parsedDates.length > 0) {
                    const minDate = new Date(Math.min(...parsedDates));
                    const maxDate = new Date(Math.max(...parsedDates));
                    
                    testDateRange = {
                        min: minDate,
                        max: maxDate,
                        minStr: minDate.toISOString().split('T')[0],
                        maxStr: maxDate.toISOString().split('T')[0]
                    };

                    logResult('data-analysis-results', `📅 Date Range: ${testDateRange.minStr} to ${testDateRange.maxStr}`, 'info');
                    logResult('data-analysis-results', `📊 Latest Available Data Date: ${testDateRange.maxStr}`, 'success');
                    
                    // Update metrics
                    document.getElementById('data-metrics').innerHTML = `
                        <div class="metric-card">
                            <div class="metric-value">${testLeadData.length}</div>
                            <div class="metric-label">Total Records</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">${testDateRange.minStr}</div>
                            <div class="metric-label">Earliest Date</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">${testDateRange.maxStr}</div>
                            <div class="metric-label">Latest Date</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">${Math.ceil((testDateRange.max - testDateRange.min) / (1000 * 60 * 60 * 24)) + 1}</div>
                            <div class="metric-label">Total Days</div>
                        </div>
                    `;
                }
                
            } catch (error) {
                logResult('data-analysis-results', `❌ Error loading data: ${error.message}`, 'error');
            }
        }

        function testDateFilter() {
            if (!testLeadData.length || !testDateRange) {
                logResult('filter-results', '❌ Please analyze data first', 'error');
                return;
            }

            const filterValue = document.getElementById('test-filter').value;
            logResult('filter-results', `🔍 Testing filter: ${filterValue}`, 'info');

            let filteredData = testLeadData;
            let dateRangeText = '';

            if (filterValue === 'all') {
                dateRangeText = `All time (${filteredData.length} records) • ${testDateRange.minStr} - ${testDateRange.maxStr}`;
            } else {
                // Calculate relative date ranges based on LATEST DATA DATE
                const daysToSubtract = {
                    'last-14': 14,
                    'last-30': 30,
                    'last-60': 60,
                    'last-90': 90
                }[filterValue] || 30;

                const latestDate = new Date(testDateRange.max);
                const startDate = new Date(latestDate);
                startDate.setDate(latestDate.getDate() - daysToSubtract + 1);

                const startDateStr = startDate.toISOString().split('T')[0];
                const endDateStr = latestDate.toISOString().split('T')[0];

                // Filter using string comparison
                filteredData = testLeadData.filter(record => {
                    const recordDateStr = record['Date Created'];
                    if (!recordDateStr) return false;
                    
                    let normalizedDateStr = recordDateStr;
                    if (recordDateStr.includes('T')) {
                        normalizedDateStr = recordDateStr.split('T')[0];
                    }
                    
                    return normalizedDateStr >= startDateStr && normalizedDateStr <= endDateStr;
                });

                dateRangeText = `Last ${daysToSubtract} days of available data (${filteredData.length} records) • ${startDateStr} - ${endDateStr}`;
                
                logResult('filter-results', `📅 Using latest available date as reference: ${testDateRange.maxStr}`, 'info');
                logResult('filter-results', `📊 Calculated range: ${startDateStr} to ${endDateStr}`, 'info');
            }

            logResult('filter-results', `✅ Filtered to ${filteredData.length} records (from ${testLeadData.length} total)`, 'success');
            
            // Update date range display
            document.getElementById('date-range-display').textContent = dateRangeText;
        }

        function analyzeTrafficSources() {
            if (!testLeadData.length) {
                logResult('traffic-source-results', '❌ Please analyze data first', 'error');
                return;
            }

            logResult('traffic-source-results', 'Analyzing traffic sources...', 'info');

            const trafficSourceCounts = {};
            testLeadData.forEach(record => {
                const source = record['Traffic Source'] || 'Other';
                trafficSourceCounts[source] = (trafficSourceCounts[source] || 0) + 1;
            });

            const googlePaid = trafficSourceCounts['Google Paid'] || 0;
            const googleOrganic = trafficSourceCounts['Google Organic'] || 0;
            const googleTotal = googlePaid + googleOrganic;
            const metaTotal = trafficSourceCounts['Meta'] || 0;
            const otherTotal = testLeadData.length - googleTotal - metaTotal;

            logResult('traffic-source-results', `✅ Traffic source analysis complete`, 'success');
            logResult('traffic-source-results', `🔍 Google Paid: ${googlePaid}`, 'info');
            logResult('traffic-source-results', `🔍 Google Organic: ${googleOrganic}`, 'info');
            logResult('traffic-source-results', `🔍 Meta: ${metaTotal}`, 'info');
            logResult('traffic-source-results', `🔍 Other: ${otherTotal}`, 'info');

            // Update traffic metrics
            document.getElementById('traffic-metrics').innerHTML = `
                <div class="metric-card">
                    <div class="metric-value">${googleTotal}</div>
                    <div class="metric-label">Google Total</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${googlePaid}</div>
                    <div class="metric-label">Google Paid</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${googleOrganic}</div>
                    <div class="metric-label">Google Organic</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${metaTotal}</div>
                    <div class="metric-label">Meta</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${otherTotal}</div>
                    <div class="metric-label">Other</div>
                </div>
            `;
        }

        // Auto-load data on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                analyzeLeadData();
            }, 1000);
        });
    </script>
</body>
</html>
