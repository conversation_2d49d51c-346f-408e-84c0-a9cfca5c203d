#!/usr/bin/env python3
"""
Verify the exact record count in the Airtable Google Ads table.
"""

import requests
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

AIRTABLE_API_KEY = os.getenv('AIRTABLE_API_KEY')
BASE_ID = 'app7ffftdM6e3yekG'
TABLE_ID = 'tblRBXdh6L6zm9CZn'  # Fresh Google Ads table

def count_airtable_records():
    """Count total records in the Airtable Google Ads table."""

    if not AIRTABLE_API_KEY:
        print("❌ AIRTABLE_API_KEY not found in environment variables")
        return None

    # Also check the legacy table
    legacy_table_id = 'tbl4QdehJluhBSSVh'

    print(f"🔍 Checking BOTH Google Ads tables:")
    print(f"   Fresh table: {TABLE_ID}")
    print(f"   Legacy table: {legacy_table_id}")
    print("=" * 60)

    # Count fresh table
    fresh_count = count_table_records(TABLE_ID, "Fresh Google Ads")

    # Count legacy table
    legacy_count = count_table_records(legacy_table_id, "Legacy Google Ads")

    return fresh_count, legacy_count

def count_table_records(table_id, table_name):
    """Count records in a specific table."""
    url = f"https://api.airtable.com/v0/{BASE_ID}/{table_id}"
    headers = {
        'Authorization': f'Bearer {AIRTABLE_API_KEY}',
        'Content-Type': 'application/json'
    }

    total_records = 0
    offset = None
    page_count = 0

    print(f"\n🔍 Counting records in {table_name}")
    print(f"📊 Table ID: {table_id}")
    print("-" * 40)
    
    while True:
        page_count += 1
        
        # Set up parameters for this page
        params = {
            'maxRecords': 100,  # Airtable API limit per request
            'sort[0][field]': 'Date',
            'sort[0][direction]': 'desc'
        }
        
        if offset:
            params['offset'] = offset
        
        print(f"📄 Fetching page {page_count}...")
        
        try:
            response = requests.get(url, headers=headers, params=params, timeout=15)
            response.raise_for_status()
            
            data = response.json()
            records = data.get('records', [])
            page_record_count = len(records)
            total_records += page_record_count
            
            print(f"   Page {page_count}: {page_record_count} records (Total so far: {total_records})")

            # Check if there are more pages
            offset = data.get('offset')
            if not offset:
                print(f"✅ No more pages. Pagination complete.")
                break

            # Safety check to prevent infinite loops
            if page_count > 50:
                print(f"⚠️ Reached maximum page limit (50). Stopping.")
                break

        except requests.exceptions.RequestException as e:
            print(f"❌ Error fetching page {page_count}: {e}")
            break

    print(f"📊 {table_name} Results: {total_records:,} records")
    return total_records

if __name__ == "__main__":
    fresh_count, legacy_count = count_airtable_records()

    print("\n" + "=" * 60)
    print(f"🎯 FINAL COMPARISON:")
    print(f"   Fresh Google Ads table: {fresh_count:,} records")
    print(f"   Legacy Google Ads table: {legacy_count:,} records")
    print(f"   Expected (from user): 1,970 records")
    print(f"   CSV file contains: 1,970 records")

    if legacy_count == 1970:
        print(f"\n✅ FOUND THE ISSUE!")
        print(f"   The app should use the LEGACY table (tbl4QdehJluhBSSVh)")
        print(f"   Legacy table has the correct 1,970 records")
    elif fresh_count == 1970:
        print(f"\n✅ Fresh table is correct")
    else:
        print(f"\n🤔 Neither table has exactly 1,970 records")
        print(f"   Fresh: {fresh_count}, Legacy: {legacy_count}, Expected: 1,970")
