<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Sales Metrics Diagnosis</title>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #121212;
            color: #ffffff;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: #1e1e1e;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        h1 {
            color: #e91e63;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            background: #2a2a2a;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #e91e63;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 6px;
            border-left: 4px solid;
        }
        .test-result.success {
            background: rgba(76, 175, 80, 0.1);
            border-color: #4caf50;
            color: #4caf50;
        }
        .test-result.error {
            background: rgba(244, 67, 54, 0.1);
            border-color: #f44336;
            color: #f44336;
        }
        .test-result.info {
            background: rgba(33, 150, 243, 0.1);
            border-color: #2196f3;
            color: #2196f3;
        }
        .test-result.warning {
            background: rgba(255, 193, 7, 0.1);
            border-color: #ffc107;
            color: #ffc107;
        }
        button {
            background: linear-gradient(135deg, #e91e63, #ff5722);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            margin: 10px 10px 10px 0;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .metric-card {
            background: #333;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        .metric-card h4 {
            color: #e91e63;
            margin-top: 0;
            margin-bottom: 10px;
        }
        .metric-value {
            font-size: 32px;
            font-weight: bold;
            color: #4caf50;
            margin-bottom: 5px;
        }
        .metric-value.suspicious {
            color: #f44336;
        }
        .metric-value.warning {
            color: #ffc107;
        }
        .metric-label {
            font-size: 14px;
            color: #aaa;
        }
        .issue-list {
            background: #333;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .issue-item {
            padding: 8px 0;
            border-bottom: 1px solid #444;
        }
        .issue-item:last-child {
            border-bottom: none;
        }
        .issue-item.critical {
            color: #f44336;
        }
        .issue-item.warning {
            color: #ffc107;
        }
        .issue-item.info {
            color: #2196f3;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔍 Sales Metrics Diagnosis</h1>
        <p>This tool diagnoses the suspicious Sales Report metrics: Total Leads (1,000), Matched Leads (0), etc.</p>

        <div class="test-section">
            <h2>📊 Current Metrics Analysis</h2>
            <button onclick="analyzeCurrentMetrics()">Analyze Current Metrics</button>
            <div id="current-metrics-results"></div>
            <div class="metrics-grid" id="current-metrics-display">
                <!-- Will be populated -->
            </div>
        </div>

        <div class="test-section">
            <h2>🔍 Data Source Investigation</h2>
            <button onclick="investigateDataSources()">Investigate Data Sources</button>
            <div id="data-sources-results"></div>
        </div>

        <div class="test-section">
            <h2>📈 Lead Data Pagination Test</h2>
            <button onclick="testLeadDataPagination()">Test Lead Data Pagination</button>
            <div id="pagination-test-results"></div>
        </div>

        <div class="test-section">
            <h2>🔗 Matching Analysis Status</h2>
            <button onclick="checkMatchingStatus()">Check Matching Status</button>
            <div id="matching-status-results"></div>
        </div>

        <div class="test-section">
            <h2>🚨 Issues Identified</h2>
            <button onclick="identifyIssues()">Identify All Issues</button>
            <div id="issues-results"></div>
            <div class="issue-list" id="issues-list">
                <!-- Will be populated -->
            </div>
        </div>

        <div class="test-section">
            <h2>🔧 Recommended Fixes</h2>
            <button onclick="generateRecommendations()">Generate Fix Recommendations</button>
            <div id="recommendations-results"></div>
        </div>
    </div>

    <script>
        let leadData = [];
        let salesData = [];

        function logResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            container.appendChild(resultDiv);
        }

        function analyzeCurrentMetrics() {
            logResult('current-metrics-results', '📊 Analyzing current Sales Report metrics...', 'info');

            // Simulate the suspicious metrics from your web app
            const suspiciousMetrics = {
                totalLeads: 1000,
                totalCustomers: 745,
                matchedLeads: 0,
                conversionRate: 0.0
            };

            const metricsDisplay = document.getElementById('current-metrics-display');
            metricsDisplay.innerHTML = `
                <div class="metric-card">
                    <h4>Total Leads</h4>
                    <div class="metric-value suspicious">${suspiciousMetrics.totalLeads.toLocaleString()}</div>
                    <div class="metric-label">🚨 Suspicious - Round number</div>
                </div>
                <div class="metric-card">
                    <h4>Total Customers</h4>
                    <div class="metric-value">${suspiciousMetrics.totalCustomers.toLocaleString()}</div>
                    <div class="metric-label">✅ Changes with filters</div>
                </div>
                <div class="metric-card">
                    <h4>Matched Leads</h4>
                    <div class="metric-value suspicious">${suspiciousMetrics.matchedLeads.toLocaleString()}</div>
                    <div class="metric-label">🚨 Always zero</div>
                </div>
                <div class="metric-card">
                    <h4>Conversion Rate</h4>
                    <div class="metric-value suspicious">${suspiciousMetrics.conversionRate.toFixed(1)}%</div>
                    <div class="metric-label">🚨 Always zero</div>
                </div>
            `;

            logResult('current-metrics-results', '🚨 Total Leads: 1,000 - This is suspicious! Likely a pagination limit.', 'error');
            logResult('current-metrics-results', '✅ Total Customers: 745 - This changes with filters, seems correct.', 'success');
            logResult('current-metrics-results', '🚨 Matched Leads: 0 - Matching analysis not run.', 'error');
            logResult('current-metrics-results', '🚨 Conversion Rate: 0.0% - Depends on matching analysis.', 'error');
        }

        async function investigateDataSources() {
            logResult('data-sources-results', '🔍 Investigating data sources...', 'info');

            try {
                // Test lead data loading
                logResult('data-sources-results', '📊 Testing lead data loading...', 'info');
                const leadResponse = await fetch('/api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblcdFVUC3zJrbmNf&maxRecords=2000');
                
                if (!leadResponse.ok) {
                    throw new Error(`Lead data HTTP ${leadResponse.status}: ${leadResponse.statusText}`);
                }
                
                const leadDataResult = await leadResponse.json();
                leadData = Array.isArray(leadDataResult) ? leadDataResult : (leadDataResult.records || []);
                
                logResult('data-sources-results', `📈 Lead data: ${leadData.length} records loaded`, leadData.length === 1000 ? 'warning' : 'success');
                
                if (leadData.length === 1000) {
                    logResult('data-sources-results', '🚨 PAGINATION ISSUE: Exactly 1,000 leads suggests maxRecords limit hit!', 'error');
                } else if (leadData.length > 1000) {
                    logResult('data-sources-results', '✅ Pagination working: Got more than 1,000 records', 'success');
                }

                // Test sales data loading
                logResult('data-sources-results', '📊 Testing sales data loading...', 'info');
                const salesResponse = await fetch('/api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblHyyZHUsTdEb3BL&maxRecords=2000');
                
                if (!salesResponse.ok) {
                    throw new Error(`Sales data HTTP ${salesResponse.status}: ${salesResponse.statusText}`);
                }
                
                const salesDataResult = await salesResponse.json();
                salesData = Array.isArray(salesDataResult) ? salesDataResult : (salesDataResult.records || []);
                
                logResult('data-sources-results', `📊 Sales data: ${salesData.length} records loaded`, 'success');

            } catch (error) {
                logResult('data-sources-results', `❌ Error investigating data sources: ${error.message}`, 'error');
            }
        }

        async function testLeadDataPagination() {
            logResult('pagination-test-results', '📈 Testing lead data pagination...', 'info');

            try {
                // Test with different maxRecords values
                const tests = [
                    { maxRecords: 100, name: 'Default limit' },
                    { maxRecords: 1000, name: 'Suspected current limit' },
                    { maxRecords: 2000, name: 'Higher limit' }
                ];

                for (const test of tests) {
                    const response = await fetch(`/api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblcdFVUC3zJrbmNf&maxRecords=${test.maxRecords}`);
                    const data = await response.json();
                    const records = Array.isArray(data) ? data : (data.records || []);
                    
                    logResult('pagination-test-results', `📊 ${test.name} (${test.maxRecords}): ${records.length} records`, 
                        records.length === test.maxRecords ? 'warning' : 'info');
                    
                    if (records.length === test.maxRecords) {
                        logResult('pagination-test-results', `⚠️ Hit the ${test.maxRecords} record limit - more data likely available`, 'warning');
                    }
                }

                // Check if the web app is using a limit
                logResult('pagination-test-results', '🔍 Analysis: If Total Leads shows exactly 1,000, the web app is likely using maxRecords=1000', 'info');
                logResult('pagination-test-results', '💡 Solution: Remove maxRecords limit or implement proper pagination', 'info');

            } catch (error) {
                logResult('pagination-test-results', `❌ Error testing pagination: ${error.message}`, 'error');
            }
        }

        function checkMatchingStatus() {
            logResult('matching-status-results', '🔗 Checking matching analysis status...', 'info');

            // Simulate checking the matching status
            logResult('matching-status-results', '🔍 Checking if matching analysis has been run...', 'info');
            logResult('matching-status-results', '❌ Matched Leads: 0 - No matching analysis performed', 'error');
            logResult('matching-status-results', '💡 Reason: Automatic matching was disabled for performance', 'info');
            logResult('matching-status-results', '🔧 Solution: Click "Run Matching" button to perform analysis', 'info');
            logResult('matching-status-results', '⚠️ Note: Matching analysis is now manual to improve filter performance', 'warning');
        }

        function identifyIssues() {
            logResult('issues-results', '🚨 Identifying all issues with Sales Report metrics...', 'info');

            const issues = [
                {
                    metric: 'Total Leads (1,000)',
                    issue: 'Pagination limit - likely maxRecords=1000',
                    severity: 'critical',
                    impact: 'Underreporting total leads, affects conversion rate calculation'
                },
                {
                    metric: 'Matched Leads (0)',
                    issue: 'Matching analysis not run',
                    severity: 'warning',
                    impact: 'Cannot calculate conversion rate, no lead-to-customer insights'
                },
                {
                    metric: 'Conversion Rate (0.0%)',
                    issue: 'Depends on matched leads being zero',
                    severity: 'warning',
                    impact: 'No conversion insights available'
                },
                {
                    metric: 'Total Customers (745)',
                    issue: 'No issue - changes with filters correctly',
                    severity: 'info',
                    impact: 'Working as expected'
                }
            ];

            const issuesList = document.getElementById('issues-list');
            issuesList.innerHTML = '';

            issues.forEach(issue => {
                const issueElement = document.createElement('div');
                issueElement.className = `issue-item ${issue.severity}`;
                issueElement.innerHTML = `
                    <strong>${issue.metric}:</strong> ${issue.issue}<br>
                    <small>Impact: ${issue.impact}</small>
                `;
                issuesList.appendChild(issueElement);

                const logType = issue.severity === 'critical' ? 'error' : 
                              issue.severity === 'warning' ? 'warning' : 'info';
                logResult('issues-results', `${issue.severity.toUpperCase()}: ${issue.metric} - ${issue.issue}`, logType);
            });

            logResult('issues-results', '📋 Summary: 1 critical issue (pagination), 2 warning issues (matching)', 'warning');
        }

        function generateRecommendations() {
            logResult('recommendations-results', '🔧 Generating fix recommendations...', 'info');

            logResult('recommendations-results', '🎯 Fix 1: Lead Data Pagination Issue', 'info');
            logResult('recommendations-results', '   • Problem: allCsvData likely limited to 1,000 records', 'error');
            logResult('recommendations-results', '   • Solution: Remove maxRecords limit or implement proper pagination', 'success');
            logResult('recommendations-results', '   • Code: Check how lead data is loaded in loadAirtableData()', 'info');

            logResult('recommendations-results', '🎯 Fix 2: Matching Analysis', 'info');
            logResult('recommendations-results', '   • Problem: Matching analysis not run (disabled for performance)', 'warning');
            logResult('recommendations-results', '   • Solution: Run matching analysis manually or make it optional', 'success');
            logResult('recommendations-results', '   • Action: Click "Run Matching" button in Sales Report', 'info');

            logResult('recommendations-results', '🎯 Fix 3: Metric Display Logic', 'info');
            logResult('recommendations-results', '   • Consider showing "Not calculated" instead of 0 for unrun matching', 'info');
            logResult('recommendations-results', '   • Add indicators when metrics depend on missing data', 'info');

            logResult('recommendations-results', '✅ Priority: Fix pagination issue first - it affects the foundation', 'success');
            logResult('recommendations-results', '📊 Expected result: Total Leads should show actual count (likely >1,000)', 'success');
        }

        // Auto-run analysis on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                analyzeCurrentMetrics();
            }, 1000);
        });
    </script>
</body>
</html>
