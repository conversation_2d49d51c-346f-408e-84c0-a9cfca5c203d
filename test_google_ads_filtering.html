<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Ads Filter Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #1a1a1a;
            color: #ffffff;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            background-color: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #444;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #2d5a2d;
            border: 1px solid #4caf50;
        }
        .error {
            background-color: #5a2d2d;
            border: 1px solid #f44336;
        }
        .info {
            background-color: #2d4a5a;
            border: 1px solid #2196f3;
        }
        button {
            background-color: #4caf50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #45a049;
        }
        .filter-controls {
            background-color: #333;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        select, input {
            background-color: #444;
            color: white;
            border: 1px solid #666;
            padding: 8px;
            border-radius: 4px;
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Google Ads Filter Accuracy Test</h1>
        <p>This page tests the restored Google Ads filtering functionality to ensure it maintains data accuracy with the complete 1,970 record dataset.</p>

        <div class="test-section">
            <h2>📊 Data Loading Test</h2>
            <button onclick="testDataLoading()">Test Data Loading</button>
            <div id="data-loading-results"></div>
        </div>

        <div class="test-section">
            <h2>🔍 Filter Function Test</h2>
            <div class="filter-controls">
                <label>Test Filter:</label>
                <select id="test-filter">
                    <option value="last-14">Last 14 Days</option>
                    <option value="last-30">Last 30 Days</option>
                    <option value="last-90">Last 90 Days</option>
                    <option value="all">All Time</option>
                </select>
                <button onclick="testFilterFunction()">Apply Test Filter</button>
            </div>
            <div id="filter-function-results"></div>
        </div>

        <div class="test-section">
            <h2>📈 UI Integration Test</h2>
            <button onclick="testUIIntegration()">Test UI Controls</button>
            <div id="ui-integration-results"></div>
        </div>

        <div class="test-section">
            <h2>🎯 Accuracy Verification</h2>
            <button onclick="testAccuracy()">Verify Filter Accuracy</button>
            <div id="accuracy-results"></div>
        </div>

        <div class="test-section">
            <h2>📋 Test Summary</h2>
            <div id="test-summary"></div>
        </div>
    </div>

    <script>
        // Test results tracking
        let testResults = {
            dataLoading: false,
            filterFunction: false,
            uiIntegration: false,
            accuracy: false
        };

        function logResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            container.appendChild(resultDiv);
        }

        function testDataLoading() {
            logResult('data-loading-results', 'Starting data loading test...', 'info');
            
            // Check if we can access the main page's data
            try {
                if (window.parent && window.parent.gadsData) {
                    const dataLength = window.parent.gadsData.length;
                    logResult('data-loading-results', `✅ Found ${dataLength} Google Ads records`, 'success');
                    
                    if (dataLength >= 1970) {
                        logResult('data-loading-results', '✅ Data loading test PASSED - Complete dataset available', 'success');
                        testResults.dataLoading = true;
                    } else {
                        logResult('data-loading-results', `❌ Data loading test FAILED - Expected 1970+ records, got ${dataLength}`, 'error');
                    }
                } else {
                    logResult('data-loading-results', '❌ Cannot access main page data - Test must be run from main dashboard', 'error');
                }
            } catch (error) {
                logResult('data-loading-results', `❌ Error accessing data: ${error.message}`, 'error');
            }
            
            updateTestSummary();
        }

        function testFilterFunction() {
            const filterValue = document.getElementById('test-filter').value;
            logResult('filter-function-results', `Testing filter: ${filterValue}`, 'info');
            
            try {
                if (window.parent && window.parent.applyGadsDateFilter) {
                    // Apply the filter
                    window.parent.applyGadsDateFilter(filterValue);
                    
                    // Check if filtered data exists
                    setTimeout(() => {
                        if (window.parent.gadsFilteredData) {
                            const filteredLength = window.parent.gadsFilteredData.length;
                            const originalLength = window.parent.gadsData ? window.parent.gadsData.length : 0;
                            
                            logResult('filter-function-results', `✅ Filter applied: ${filteredLength} records (from ${originalLength} total)`, 'success');
                            
                            if (filterValue === 'all' && filteredLength === originalLength) {
                                logResult('filter-function-results', '✅ "All Time" filter working correctly', 'success');
                                testResults.filterFunction = true;
                            } else if (filterValue !== 'all' && filteredLength < originalLength && filteredLength > 0) {
                                logResult('filter-function-results', '✅ Date filter working correctly', 'success');
                                testResults.filterFunction = true;
                            } else {
                                logResult('filter-function-results', '❌ Filter results unexpected', 'error');
                            }
                        } else {
                            logResult('filter-function-results', '❌ No filtered data found', 'error');
                        }
                        updateTestSummary();
                    }, 500);
                } else {
                    logResult('filter-function-results', '❌ Filter function not accessible', 'error');
                    updateTestSummary();
                }
            } catch (error) {
                logResult('filter-function-results', `❌ Error testing filter: ${error.message}`, 'error');
                updateTestSummary();
            }
        }

        function testUIIntegration() {
            logResult('ui-integration-results', 'Testing UI integration...', 'info');
            
            try {
                if (window.parent && window.parent.document) {
                    const parentDoc = window.parent.document;
                    
                    // Check if filter elements exist and are enabled
                    const dateFilter = parentDoc.getElementById('gads-date-filter');
                    const grouping = parentDoc.getElementById('gads-grouping');
                    const refreshBtn = parentDoc.getElementById('gads-refresh-data');
                    
                    let uiTests = 0;
                    let uiPassed = 0;
                    
                    if (dateFilter) {
                        uiTests++;
                        if (!dateFilter.disabled) {
                            logResult('ui-integration-results', '✅ Date filter is enabled', 'success');
                            uiPassed++;
                        } else {
                            logResult('ui-integration-results', '❌ Date filter is still disabled', 'error');
                        }
                    }
                    
                    if (grouping) {
                        uiTests++;
                        if (!grouping.disabled) {
                            logResult('ui-integration-results', '✅ Grouping filter is enabled', 'success');
                            uiPassed++;
                        } else {
                            logResult('ui-integration-results', '❌ Grouping filter is still disabled', 'error');
                        }
                    }
                    
                    if (refreshBtn) {
                        uiTests++;
                        if (!refreshBtn.disabled) {
                            logResult('ui-integration-results', '✅ Refresh button is enabled', 'success');
                            uiPassed++;
                        } else {
                            logResult('ui-integration-results', '❌ Refresh button is still disabled', 'error');
                        }
                    }
                    
                    if (uiPassed === uiTests && uiTests > 0) {
                        logResult('ui-integration-results', '✅ UI integration test PASSED', 'success');
                        testResults.uiIntegration = true;
                    } else {
                        logResult('ui-integration-results', `❌ UI integration test FAILED (${uiPassed}/${uiTests} passed)`, 'error');
                    }
                } else {
                    logResult('ui-integration-results', '❌ Cannot access parent document', 'error');
                }
            } catch (error) {
                logResult('ui-integration-results', `❌ Error testing UI: ${error.message}`, 'error');
            }
            
            updateTestSummary();
        }

        function testAccuracy() {
            logResult('accuracy-results', 'Testing filter accuracy...', 'info');
            
            try {
                if (window.parent && window.parent.gadsData) {
                    const allData = window.parent.gadsData;
                    logResult('accuracy-results', `Total dataset: ${allData.length} records`, 'info');
                    
                    // Test Last 14 Days filter
                    window.parent.applyGadsDateFilter('last-14');
                    
                    setTimeout(() => {
                        const filtered14 = window.parent.gadsFilteredData;
                        if (filtered14) {
                            logResult('accuracy-results', `Last 14 days: ${filtered14.length} records`, 'info');
                            
                            // Test Last 30 Days filter
                            window.parent.applyGadsDateFilter('last-30');
                            
                            setTimeout(() => {
                                const filtered30 = window.parent.gadsFilteredData;
                                if (filtered30) {
                                    logResult('accuracy-results', `Last 30 days: ${filtered30.length} records`, 'info');
                                    
                                    // Verify logical consistency
                                    if (filtered30.length >= filtered14.length) {
                                        logResult('accuracy-results', '✅ Filter logic is consistent (30 days ≥ 14 days)', 'success');
                                        testResults.accuracy = true;
                                    } else {
                                        logResult('accuracy-results', '❌ Filter logic inconsistent (30 days < 14 days)', 'error');
                                    }
                                    
                                    // Test All Time filter
                                    window.parent.applyGadsDateFilter('all');
                                    setTimeout(() => {
                                        const filteredAll = window.parent.gadsFilteredData;
                                        if (filteredAll && filteredAll.length === allData.length) {
                                            logResult('accuracy-results', '✅ "All Time" filter returns complete dataset', 'success');
                                        } else {
                                            logResult('accuracy-results', '❌ "All Time" filter not working correctly', 'error');
                                            testResults.accuracy = false;
                                        }
                                        updateTestSummary();
                                    }, 300);
                                }
                            }, 300);
                        }
                    }, 300);
                } else {
                    logResult('accuracy-results', '❌ Cannot access data for accuracy testing', 'error');
                    updateTestSummary();
                }
            } catch (error) {
                logResult('accuracy-results', `❌ Error testing accuracy: ${error.message}`, 'error');
                updateTestSummary();
            }
        }

        function updateTestSummary() {
            const summary = document.getElementById('test-summary');
            const passed = Object.values(testResults).filter(result => result).length;
            const total = Object.keys(testResults).length;
            
            let summaryHTML = `<h3>Test Results: ${passed}/${total} Passed</h3>`;
            
            for (const [test, result] of Object.entries(testResults)) {
                const status = result ? '✅ PASSED' : '❌ FAILED';
                const testName = test.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
                summaryHTML += `<div class="test-result ${result ? 'success' : 'error'}">${testName}: ${status}</div>`;
            }
            
            if (passed === total) {
                summaryHTML += '<div class="test-result success"><strong>🎉 ALL TESTS PASSED! Google Ads filtering is working correctly.</strong></div>';
            } else {
                summaryHTML += '<div class="test-result error"><strong>⚠️ Some tests failed. Please review the results above.</strong></div>';
            }
            
            summary.innerHTML = summaryHTML;
        }

        // Initialize
        updateTestSummary();
    </script>
</body>
</html>
