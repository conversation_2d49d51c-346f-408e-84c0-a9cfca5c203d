#!/usr/bin/env python3
"""
Test the server fix by making a direct request to our server.
"""

import requests
import json

def test_server_pagination():
    """Test our server's pagination fix."""
    
    url = "http://localhost:8000/api/airtable/records"
    params = {
        'baseId': 'app7ffftdM6e3yekG',
        'tableId': 'tblRBXdh6L6zm9CZn',
        'maxRecords': 3000  # Request more than 1780 to test pagination
    }
    
    print(f"🔍 Testing server pagination fix...")
    print(f"📊 URL: {url}")
    print(f"📊 Params: {params}")
    print("=" * 60)
    
    try:
        response = requests.get(url, params=params, timeout=30)
        response.raise_for_status()
        
        data = response.json()
        records = data.get('records', [])
        pagination_info = data.get('pagination_info', {})
        
        print(f"✅ Response received successfully")
        print(f"📊 Total records: {len(records):,}")
        print(f"📊 Pagination info: {pagination_info}")
        
        if len(records) >= 1970:
            print(f"✅ SUCCESS: Got {len(records):,} records (expected ~1970)")
            
            # Calculate total clicks
            total_clicks = 0
            for record in records:
                clicks = int(record.get('Clicks', 0) or 0)
                total_clicks += clicks
            
            print(f"📊 Total Clicks calculated: {total_clicks:,}")
            print(f"📊 Expected: 2,021")
            
            if total_clicks == 2021:
                print(f"🎯 PERFECT: Total clicks match expected value!")
            else:
                print(f"⚠️ Clicks don't match. Difference: {2021 - total_clicks}")
                
        else:
            print(f"❌ STILL BROKEN: Only got {len(records):,} records (expected ~1970)")
            
        # Show date range
        if records:
            dates = [record.get('Date') for record in records if record.get('Date')]
            if dates:
                print(f"📅 Date range: {min(dates)} to {max(dates)}")
                print(f"📅 Unique dates: {len(set(dates))}")
        
        return len(records), total_clicks if 'total_clicks' in locals() else 0
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return 0, 0
    except Exception as e:
        print(f"❌ Error: {e}")
        return 0, 0

if __name__ == "__main__":
    record_count, click_count = test_server_pagination()
    
    print("\n" + "=" * 60)
    print(f"🎯 FINAL RESULTS:")
    print(f"   Records fetched: {record_count:,}")
    print(f"   Total clicks: {click_count:,}")
    print(f"   Expected records: 1,970")
    print(f"   Expected clicks: 2,021")
    
    if record_count >= 1970 and click_count == 2021:
        print(f"✅ FIX SUCCESSFUL!")
    else:
        print(f"❌ FIX FAILED - Need more investigation")
