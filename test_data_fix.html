<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Fix Verification</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #1a1a1a;
            color: #ffffff;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .result {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border: 1px solid #444;
        }
        .success {
            background-color: #2d5a2d;
            border-color: #4caf50;
        }
        .error {
            background-color: #5a2d2d;
            border-color: #f44336;
        }
        .info {
            background-color: #2d4a5a;
            border-color: #2196f3;
        }
        button {
            background-color: #4caf50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 16px;
        }
        button:hover {
            background-color: #45a049;
        }
        .metric {
            font-size: 24px;
            font-weight: bold;
            color: #4caf50;
        }
        .expected {
            color: #4caf50;
        }
        .actual {
            color: #2196f3;
        }
        .mismatch {
            color: #f44336;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🚨 URGENT: Data Fix Verification</h1>
        <p>Testing if the maxRecords fix restored the complete 1,970 record dataset and 2,021 total clicks.</p>

        <button onclick="testDirectAirtableCall()">Test Direct Airtable Call</button>
        <button onclick="testRecordCount()">Test Record Count</button>
        <button onclick="testTotalClicks()">Test Total Clicks</button>

        <div id="results"></div>
    </div>

    <script>
        function logResult(message, type = 'info') {
            const results = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            results.appendChild(resultDiv);
        }

        async function testDirectAirtableCall() {
            logResult('🔄 Testing direct Airtable API call with new maxRecords default...', 'info');
            
            try {
                const response = await fetch('/api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblRBXdh6L6zm9CZn');
                const data = await response.json();
                
                if (response.ok) {
                    const recordCount = data.records ? data.records.length : 0;
                    const paginationInfo = data.pagination_info;
                    
                    logResult(`📊 Direct API call result: ${recordCount} records`, 'info');
                    
                    if (paginationInfo) {
                        logResult(`📄 Pagination: ${paginationInfo.total_records} total from ${paginationInfo.pages_fetched} pages`, 'info');
                    }
                    
                    if (recordCount >= 1970) {
                        logResult(`✅ SUCCESS: Got ${recordCount} records (≥ 1970 expected)`, 'success');
                    } else {
                        logResult(`❌ FAILED: Only got ${recordCount} records (expected ≥ 1970)`, 'error');
                    }
                    
                    // Test total clicks calculation
                    if (data.records && data.records.length > 0) {
                        let totalClicks = 0;
                        data.records.forEach(record => {
                            totalClicks += parseInt(record['Clicks'] || 0);
                        });
                        
                        logResult(`🖱️ Total clicks calculated: <span class="metric">${totalClicks}</span>`, 'info');
                        
                        if (totalClicks === 2021) {
                            logResult(`✅ PERFECT: Total clicks = 2021 (exact match!)`, 'success');
                        } else if (totalClicks > 2000) {
                            logResult(`✅ GOOD: Total clicks = ${totalClicks} (close to expected 2021)`, 'success');
                        } else {
                            logResult(`❌ PROBLEM: Total clicks = ${totalClicks} (expected ~2021)`, 'error');
                        }
                    }
                } else {
                    logResult(`❌ API Error: ${response.status} - ${data.error || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                logResult(`❌ Network Error: ${error.message}`, 'error');
            }
        }

        async function testRecordCount() {
            logResult('🔢 Testing record count specifically...', 'info');
            
            try {
                // Test with explicit maxRecords parameter
                const response = await fetch('/api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblRBXdh6L6zm9CZn&maxRecords=10000');
                const data = await response.json();
                
                if (response.ok) {
                    const recordCount = data.records ? data.records.length : 0;
                    logResult(`📊 With maxRecords=10000: ${recordCount} records`, 'info');
                    
                    if (recordCount === 1970) {
                        logResult(`✅ EXACT MATCH: ${recordCount} records (perfect!)`, 'success');
                    } else if (recordCount > 1900) {
                        logResult(`✅ CLOSE: ${recordCount} records (very close to 1970)`, 'success');
                    } else {
                        logResult(`❌ TOO LOW: ${recordCount} records (expected ~1970)`, 'error');
                    }
                } else {
                    logResult(`❌ API Error: ${response.status}`, 'error');
                }
            } catch (error) {
                logResult(`❌ Error: ${error.message}`, 'error');
            }
        }

        async function testTotalClicks() {
            logResult('🖱️ Testing total clicks calculation...', 'info');
            
            try {
                const response = await fetch('/api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblRBXdh6L6zm9CZn&maxRecords=10000');
                const data = await response.json();
                
                if (response.ok && data.records) {
                    let totalClicks = 0;
                    let validRecords = 0;
                    let invalidRecords = 0;
                    
                    data.records.forEach(record => {
                        const clicks = parseInt(record['Clicks'] || 0);
                        if (!isNaN(clicks)) {
                            totalClicks += clicks;
                            validRecords++;
                        } else {
                            invalidRecords++;
                        }
                    });
                    
                    logResult(`📊 Processed ${validRecords} valid records, ${invalidRecords} invalid`, 'info');
                    logResult(`🖱️ Total Clicks: <span class="metric">${totalClicks}</span>`, 'info');
                    
                    // Compare with expected value
                    const expected = 2021;
                    const difference = Math.abs(totalClicks - expected);
                    const percentDiff = (difference / expected) * 100;
                    
                    if (totalClicks === expected) {
                        logResult(`🎯 PERFECT MATCH: ${totalClicks} = ${expected}`, 'success');
                    } else if (percentDiff < 5) {
                        logResult(`✅ VERY CLOSE: ${totalClicks} vs ${expected} (${percentDiff.toFixed(1)}% difference)`, 'success');
                    } else {
                        logResult(`❌ SIGNIFICANT DIFFERENCE: ${totalClicks} vs ${expected} (${percentDiff.toFixed(1)}% difference)`, 'error');
                    }
                } else {
                    logResult(`❌ No data received`, 'error');
                }
            } catch (error) {
                logResult(`❌ Error: ${error.message}`, 'error');
            }
        }

        // Auto-run tests on page load
        window.addEventListener('load', () => {
            logResult('🚀 Starting automatic data fix verification...', 'info');
            setTimeout(() => {
                testDirectAirtableCall();
            }, 1000);
        });
    </script>
</body>
</html>
