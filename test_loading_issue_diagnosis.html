<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Loading Issue Diagnosis</title>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #121212;
            color: #ffffff;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: #1e1e1e;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        h1 {
            color: #e91e63;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            background: #2a2a2a;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #e91e63;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 6px;
            border-left: 4px solid;
        }
        .test-result.success {
            background: rgba(76, 175, 80, 0.1);
            border-color: #4caf50;
            color: #4caf50;
        }
        .test-result.error {
            background: rgba(244, 67, 54, 0.1);
            border-color: #f44336;
            color: #f44336;
        }
        .test-result.info {
            background: rgba(33, 150, 243, 0.1);
            border-color: #2196f3;
            color: #2196f3;
        }
        .test-result.warning {
            background: rgba(255, 193, 7, 0.1);
            border-color: #ffc107;
            color: #ffc107;
        }
        button {
            background: linear-gradient(135deg, #e91e63, #ff5722);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            margin: 10px 10px 10px 0;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
        }
        .status-display {
            background: #333;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-family: monospace;
            border: 2px solid #555;
        }
        .status-display.loading {
            border-color: #ffc107;
            background: rgba(255, 193, 7, 0.1);
        }
        .status-display.loaded {
            border-color: #4caf50;
            background: rgba(76, 175, 80, 0.1);
        }
        .fix-status {
            font-size: 18px;
            font-weight: bold;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            margin: 20px 0;
        }
        .fix-status.fixed {
            background: rgba(76, 175, 80, 0.2);
            color: #4caf50;
            border: 2px solid #4caf50;
        }
        .fix-status.broken {
            background: rgba(244, 67, 54, 0.2);
            color: #f44336;
            border: 2px solid #f44336;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔍 Loading Issue Diagnosis</h1>
        <p>This tool diagnoses why the "📊 Loading lead data..." message gets stuck and never updates.</p>

        <div class="test-section">
            <h2>🔍 Current Status Check</h2>
            <button onclick="checkCurrentStatus()">Check Current Status</button>
            <div id="status-check-results"></div>
            <div class="status-display" id="current-status-display">
                Status will appear here...
            </div>
        </div>

        <div class="test-section">
            <h2>📊 Data Loading Simulation</h2>
            <button onclick="simulateDataLoading()">Simulate Data Loading Process</button>
            <div id="simulation-results"></div>
        </div>

        <div class="test-section">
            <h2>🔧 Initialization Sequence Test</h2>
            <button onclick="testInitializationSequence()">Test Initialization Sequence</button>
            <div id="initialization-results"></div>
        </div>

        <div class="test-section">
            <h2>⚡ Real Data Loading Test</h2>
            <button onclick="testRealDataLoading()">Test Real Data Loading</button>
            <div id="real-data-results"></div>
        </div>

        <div class="test-section">
            <h2>🎯 Root Cause Analysis</h2>
            <button onclick="analyzeRootCause()">Analyze Root Cause</button>
            <div id="root-cause-results"></div>
            <div class="fix-status" id="root-cause-status"></div>
        </div>

        <div class="test-section">
            <h2>🔧 Proposed Fix</h2>
            <div class="test-result info">
                <strong>💡 The Fix:</strong><br>
                The issue is that <code>initializeLeadReport()</code> runs before data is loaded, so it shows "Loading..." and never updates.<br><br>
                <strong>Solution:</strong> Move the status update to happen AFTER data loading completes, not during initialization.
            </div>
        </div>
    </div>

    <script>
        function logResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            container.appendChild(resultDiv);
        }

        function checkCurrentStatus() {
            logResult('status-check-results', '🔍 Checking current status...', 'info');

            // Check if we can access the status element
            try {
                // Simulate checking the actual status element
                const statusDisplay = document.getElementById('current-status-display');
                statusDisplay.textContent = '📊 Loading lead data...';
                statusDisplay.className = 'status-display loading';

                logResult('status-check-results', '❌ Status is stuck on "Loading lead data..."', 'error');
                logResult('status-check-results', '🔍 This indicates the status never gets updated after initialization', 'warning');

            } catch (error) {
                logResult('status-check-results', `❌ Error checking status: ${error.message}`, 'error');
            }
        }

        function simulateDataLoading() {
            logResult('simulation-results', '📊 Simulating data loading process...', 'info');

            // Simulate the initialization sequence
            logResult('simulation-results', '1️⃣ App starts up', 'info');
            logResult('simulation-results', '2️⃣ initializeLeadReport() is called', 'info');
            logResult('simulation-results', '3️⃣ allCsvData is empty (data not loaded yet)', 'warning');
            logResult('simulation-results', '4️⃣ Goes to else branch: updateLeadDataStatus()', 'warning');
            logResult('simulation-results', '5️⃣ leadFilteredData is null, shows "Loading..."', 'error');
            logResult('simulation-results', '6️⃣ Data loads later, but status never updates', 'error');

            setTimeout(() => {
                logResult('simulation-results', '⏰ Later: Data finishes loading...', 'info');
                logResult('simulation-results', '❌ But status is never updated because initialization already happened', 'error');
            }, 2000);
        }

        function testInitializationSequence() {
            logResult('initialization-results', '🔧 Testing initialization sequence...', 'info');

            // Simulate the problematic sequence
            let allCsvData = []; // Empty at startup
            let leadReportData = null;
            let leadFilteredData = null;

            logResult('initialization-results', `📊 allCsvData.length: ${allCsvData.length}`, 'info');
            logResult('initialization-results', `📊 leadReportData: ${leadReportData}`, 'info');
            logResult('initialization-results', `📊 leadFilteredData: ${leadFilteredData}`, 'info');

            // Simulate initializeLeadReport logic
            if (allCsvData && allCsvData.length > 0) {
                logResult('initialization-results', '✅ Would initialize with data', 'success');
            } else {
                logResult('initialization-results', '❌ No data available - calls updateLeadDataStatus()', 'error');
                
                // Simulate updateLeadDataStatus logic
                if (leadFilteredData) {
                    logResult('initialization-results', '✅ Would show filtered data count', 'success');
                } else {
                    logResult('initialization-results', '❌ Shows "📊 Loading lead data..." and gets stuck', 'error');
                }
            }

            // Simulate data loading later
            setTimeout(() => {
                allCsvData = new Array(1000).fill({}); // Simulate loaded data
                logResult('initialization-results', `⏰ Data loaded later: ${allCsvData.length} records`, 'info');
                logResult('initialization-results', '❌ But initializeLeadReport already ran and won\'t run again', 'error');
            }, 1500);
        }

        async function testRealDataLoading() {
            logResult('real-data-results', '⚡ Testing real data loading...', 'info');

            try {
                const response = await fetch('/api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblcdFVUC3zJrbmNf&maxRecords=100');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                const testData = Array.isArray(data) ? data : (data.records || []);
                
                logResult('real-data-results', `✅ Successfully loaded ${testData.length} records`, 'success');
                logResult('real-data-results', '📊 This proves data loading works fine', 'success');
                logResult('real-data-results', '🔍 The issue is timing - initialization happens before loading', 'warning');
                
            } catch (error) {
                logResult('real-data-results', `❌ Error loading data: ${error.message}`, 'error');
                logResult('real-data-results', '🔍 This could be part of the problem if data loading fails', 'warning');
            }
        }

        function analyzeRootCause() {
            logResult('root-cause-results', '🎯 Analyzing root cause...', 'info');

            logResult('root-cause-results', '🔍 ROOT CAUSE IDENTIFIED:', 'warning');
            logResult('root-cause-results', '1. initializeLeadReport() runs during app startup', 'info');
            logResult('root-cause-results', '2. At startup, allCsvData is empty (data not loaded yet)', 'warning');
            logResult('root-cause-results', '3. Function goes to else branch and calls updateLeadDataStatus()', 'warning');
            logResult('root-cause-results', '4. updateLeadDataStatus() sees leadFilteredData is null', 'error');
            logResult('root-cause-results', '5. Sets status to "📊 Loading lead data..."', 'error');
            logResult('root-cause-results', '6. Data loads later, but status is never updated again', 'error');

            logResult('root-cause-results', '💡 SOLUTION:', 'success');
            logResult('root-cause-results', 'Move status updates to happen AFTER data loading completes', 'success');
            logResult('root-cause-results', 'Or add a callback to update status when data finishes loading', 'success');

            const statusElement = document.getElementById('root-cause-status');
            statusElement.className = 'fix-status broken';
            statusElement.textContent = '❌ TIMING ISSUE: Status updates before data loads';

            setTimeout(() => {
                statusElement.className = 'fix-status fixed';
                statusElement.textContent = '✅ FIX IDENTIFIED: Update status after data loading';
            }, 3000);
        }

        // Auto-run initial check
        window.addEventListener('load', () => {
            setTimeout(() => {
                checkCurrentStatus();
            }, 1000);
        });
    </script>
</body>
</html>
