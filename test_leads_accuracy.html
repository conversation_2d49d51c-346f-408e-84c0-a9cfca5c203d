<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Leads Report Accuracy Test</title>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #121212;
            color: #ffffff;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: #1e1e1e;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        h1 {
            color: #e91e63;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            background: #2a2a2a;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #e91e63;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 6px;
            border-left: 4px solid;
        }
        .test-result.success {
            background: rgba(76, 175, 80, 0.1);
            border-color: #4caf50;
            color: #4caf50;
        }
        .test-result.error {
            background: rgba(244, 67, 54, 0.1);
            border-color: #f44336;
            color: #f44336;
        }
        .test-result.info {
            background: rgba(33, 150, 243, 0.1);
            border-color: #2196f3;
            color: #2196f3;
        }
        button {
            background: linear-gradient(135deg, #e91e63, #ff5722);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            margin: 10px 10px 10px 0;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .metric-card {
            background: #333;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #e91e63;
        }
        .metric-label {
            font-size: 14px;
            color: #aaa;
            margin-top: 5px;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .comparison-table th,
        .comparison-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #444;
        }
        .comparison-table th {
            background: #333;
            color: #e91e63;
        }
        .filter-controls {
            margin: 20px 0;
        }
        .filter-controls select {
            background: #333;
            color: white;
            border: 1px solid #555;
            padding: 8px 12px;
            border-radius: 4px;
            margin: 0 10px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Leads Report Accuracy Test</h1>
        <p>This page tests the accuracy of your Leads Report by comparing Airtable data with your web app's calculations.</p>

        <div class="test-section">
            <h2>📊 Data Loading Test</h2>
            <button onclick="testDataLoading()">Load & Analyze Airtable Data</button>
            <div id="data-loading-results"></div>
        </div>

        <div class="test-section">
            <h2>🔍 Traffic Source Analysis</h2>
            <button onclick="analyzeTrafficSources()">Analyze Traffic Sources</button>
            <div id="traffic-source-results"></div>
            <div class="metrics-grid" id="traffic-metrics"></div>
        </div>

        <div class="test-section">
            <h2>📅 Date Range Testing</h2>
            <div class="filter-controls">
                <label>Test Date Range:</label>
                <select id="date-range-filter">
                    <option value="all">All Time</option>
                    <option value="last-14">Last 14 Days</option>
                    <option value="last-30">Last 30 Days</option>
                    <option value="last-60">Last 60 Days</option>
                    <option value="last-90">Last 90 Days</option>
                </select>
                <button onclick="testDateFiltering()">Test Date Filtering</button>
            </div>
            <div id="date-filtering-results"></div>
            <table class="comparison-table" id="comparison-table" style="display: none;">
                <thead>
                    <tr>
                        <th>Metric</th>
                        <th>Airtable Count</th>
                        <th>Expected Web App</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody id="comparison-tbody"></tbody>
            </table>
        </div>

        <div class="test-section">
            <h2>🎯 Accuracy Summary</h2>
            <button onclick="generateAccuracySummary()">Generate Full Accuracy Report</button>
            <div id="accuracy-summary"></div>
        </div>
    </div>

    <script>
        let airtableData = [];
        let testResults = {
            dataLoading: false,
            trafficSources: false,
            dateFiltering: false
        };

        function logResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            container.appendChild(resultDiv);
        }

        async function testDataLoading() {
            logResult('data-loading-results', 'Loading data from Airtable...', 'info');
            
            try {
                // Fetch data from your Airtable API endpoint
                const response = await fetch('/api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblcdFVUC3zJrbmNf&maxRecords=2000');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                airtableData = Array.isArray(data) ? data : (data.records || []);
                
                logResult('data-loading-results', `✅ Successfully loaded ${airtableData.length} records from Airtable`, 'success');
                logResult('data-loading-results', `📊 Sample record: ${JSON.stringify(airtableData[0] || {}, null, 2)}`, 'info');
                
                testResults.dataLoading = true;
                return true;
                
            } catch (error) {
                logResult('data-loading-results', `❌ Error loading data: ${error.message}`, 'error');
                return false;
            }
        }

        function analyzeTrafficSources() {
            if (!airtableData.length) {
                logResult('traffic-source-results', '❌ No data loaded. Please run data loading test first.', 'error');
                return;
            }

            logResult('traffic-source-results', 'Analyzing traffic sources...', 'info');

            // Count traffic sources exactly as your web app does
            const trafficSourceCounts = {};
            const sourceStats = {
                google: 0,
                meta: 0,
                other: 0
            };

            airtableData.forEach(record => {
                const source = record['Traffic Source'] || 'Other';
                
                // Count exact traffic sources
                trafficSourceCounts[source] = (trafficSourceCounts[source] || 0) + 1;
                
                // Count by category (matching your web app logic)
                const sourceLower = source.toLowerCase();
                if (sourceLower.includes('google') || sourceLower.includes('adwords') || sourceLower.includes('gads')) {
                    sourceStats.google++;
                } else if (sourceLower.includes('facebook') || sourceLower.includes('meta') || sourceLower.includes('fb')) {
                    sourceStats.meta++;
                } else {
                    sourceStats.other++;
                }
            });

            // Calculate specific Google breakdown
            const googlePaid = trafficSourceCounts['Google Paid'] || 0;
            const googleOrganic = trafficSourceCounts['Google Organic'] || 0;
            const googleTotal = googlePaid + googleOrganic;
            const metaTotal = trafficSourceCounts['Meta'] || 0;

            // Display results
            logResult('traffic-source-results', `✅ Traffic source analysis complete`, 'success');
            logResult('traffic-source-results', `📊 Total Records: ${airtableData.length}`, 'info');
            logResult('traffic-source-results', `🔍 Google Paid: ${googlePaid}`, 'info');
            logResult('traffic-source-results', `🔍 Google Organic: ${googleOrganic}`, 'info');
            logResult('traffic-source-results', `🔍 Google Total: ${googleTotal}`, 'info');
            logResult('traffic-source-results', `🔍 Meta: ${metaTotal}`, 'info');
            logResult('traffic-source-results', `🔍 Other Sources: ${sourceStats.other}`, 'info');

            // Create metrics cards
            const metricsContainer = document.getElementById('traffic-metrics');
            metricsContainer.innerHTML = `
                <div class="metric-card">
                    <div class="metric-value">${airtableData.length}</div>
                    <div class="metric-label">Total Leads</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${googleTotal}</div>
                    <div class="metric-label">Google Leads</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${googlePaid}</div>
                    <div class="metric-label">Google Paid</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${googleOrganic}</div>
                    <div class="metric-label">Google Organic</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${metaTotal}</div>
                    <div class="metric-label">Meta Leads</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${sourceStats.other}</div>
                    <div class="metric-label">Other Sources</div>
                </div>
            `;

            testResults.trafficSources = true;
        }

        function testDateFiltering() {
            if (!airtableData.length) {
                logResult('date-filtering-results', '❌ No data loaded. Please run data loading test first.', 'error');
                return;
            }

            const filterValue = document.getElementById('date-range-filter').value;
            logResult('date-filtering-results', `Testing date filter: ${filterValue}`, 'info');

            let filteredData = airtableData;
            
            if (filterValue !== 'all') {
                const today = new Date();
                const daysBack = parseInt(filterValue.replace('last-', ''));
                const cutoffDate = new Date();
                cutoffDate.setDate(today.getDate() - daysBack);

                filteredData = airtableData.filter(record => {
                    const recordDate = new Date(record['Date Created']);
                    return recordDate >= cutoffDate;
                });
            }

            // Analyze filtered data
            const trafficSourceCounts = {};
            filteredData.forEach(record => {
                const source = record['Traffic Source'] || 'Other';
                trafficSourceCounts[source] = (trafficSourceCounts[source] || 0) + 1;
            });

            const googlePaid = trafficSourceCounts['Google Paid'] || 0;
            const googleOrganic = trafficSourceCounts['Google Organic'] || 0;
            const googleTotal = googlePaid + googleOrganic;
            const metaTotal = trafficSourceCounts['Meta'] || 0;
            const otherTotal = filteredData.length - googleTotal - metaTotal;

            logResult('date-filtering-results', `✅ Filtered to ${filteredData.length} records`, 'success');

            // Show comparison table
            const table = document.getElementById('comparison-table');
            const tbody = document.getElementById('comparison-tbody');
            
            tbody.innerHTML = `
                <tr>
                    <td>Total Leads</td>
                    <td>${filteredData.length}</td>
                    <td>Check your web app</td>
                    <td>⏳ Manual verification needed</td>
                </tr>
                <tr>
                    <td>Google Leads</td>
                    <td>${googleTotal}</td>
                    <td>Check your web app</td>
                    <td>⏳ Manual verification needed</td>
                </tr>
                <tr>
                    <td>Google Paid</td>
                    <td>${googlePaid}</td>
                    <td>Check your web app</td>
                    <td>⏳ Manual verification needed</td>
                </tr>
                <tr>
                    <td>Google Organic</td>
                    <td>${googleOrganic}</td>
                    <td>Check your web app</td>
                    <td>⏳ Manual verification needed</td>
                </tr>
                <tr>
                    <td>Meta Leads</td>
                    <td>${metaTotal}</td>
                    <td>Check your web app</td>
                    <td>⏳ Manual verification needed</td>
                </tr>
                <tr>
                    <td>Other Sources</td>
                    <td>${otherTotal}</td>
                    <td>Check your web app</td>
                    <td>⏳ Manual verification needed</td>
                </tr>
            `;
            
            table.style.display = 'table';
            testResults.dateFiltering = true;
        }

        function generateAccuracySummary() {
            const container = document.getElementById('accuracy-summary');
            const passedTests = Object.values(testResults).filter(Boolean).length;
            const totalTests = Object.keys(testResults).length;
            
            let summaryHTML = `
                <div class="test-result ${passedTests === totalTests ? 'success' : 'info'}">
                    <strong>📊 Test Summary: ${passedTests}/${totalTests} tests completed</strong>
                </div>
            `;
            
            if (passedTests === totalTests) {
                summaryHTML += `
                    <div class="test-result success">
                        <strong>🎉 All tests completed! Compare the numbers above with your web app's Leads Report.</strong>
                    </div>
                    <div class="test-result info">
                        <strong>📋 Next Steps:</strong><br>
                        1. Open your web app's Leads Report tab<br>
                        2. Apply the same date filters you tested here<br>
                        3. Compare the numbers with the results above<br>
                        4. Report any discrepancies for further investigation
                    </div>
                `;
            }
            
            container.innerHTML = summaryHTML;
        }

        // Auto-load data on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                testDataLoading();
            }, 1000);
        });
    </script>
</body>
</html>
