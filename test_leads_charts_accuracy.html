<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Leads Charts Accuracy Test</title>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #121212;
            color: #ffffff;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }
        .test-container {
            max-width: 1400px;
            margin: 0 auto;
            background: #1e1e1e;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        h1 {
            color: #e91e63;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            background: #2a2a2a;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #e91e63;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 6px;
            border-left: 4px solid;
        }
        .test-result.success {
            background: rgba(76, 175, 80, 0.1);
            border-color: #4caf50;
            color: #4caf50;
        }
        .test-result.error {
            background: rgba(244, 67, 54, 0.1);
            border-color: #f44336;
            color: #f44336;
        }
        .test-result.info {
            background: rgba(33, 150, 243, 0.1);
            border-color: #2196f3;
            color: #2196f3;
        }
        .test-result.warning {
            background: rgba(255, 193, 7, 0.1);
            border-color: #ffc107;
            color: #ffc107;
        }
        button {
            background: linear-gradient(135deg, #e91e63, #ff5722);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            margin: 10px 10px 10px 0;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .metric-card {
            background: #333;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #e91e63;
        }
        .metric-label {
            font-size: 14px;
            color: #aaa;
            margin-top: 5px;
        }
        .filter-controls {
            margin: 20px 0;
        }
        .filter-controls select {
            background: #333;
            color: white;
            border: 1px solid #555;
            padding: 8px 12px;
            border-radius: 4px;
            margin: 0 10px;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 14px;
        }
        .comparison-table th,
        .comparison-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #444;
        }
        .comparison-table th {
            background: #333;
            color: #e91e63;
            font-weight: 600;
        }
        .comparison-table tr:nth-child(even) {
            background: rgba(255, 255, 255, 0.02);
        }
        .chart-data-section {
            background: #333;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .accuracy-score {
            font-size: 18px;
            font-weight: bold;
            padding: 10px;
            border-radius: 6px;
            text-align: center;
            margin: 15px 0;
        }
        .accuracy-score.excellent {
            background: rgba(76, 175, 80, 0.2);
            color: #4caf50;
        }
        .accuracy-score.good {
            background: rgba(255, 193, 7, 0.2);
            color: #ffc107;
        }
        .accuracy-score.poor {
            background: rgba(244, 67, 54, 0.2);
            color: #f44336;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Leads Charts & Visualizations Accuracy Test</h1>
        <p>This comprehensive test validates that all Leads Report charts and visualizations display accurate data based on the filtered dataset.</p>

        <div class="test-section">
            <h2>📊 Data Loading & Analysis</h2>
            <button onclick="loadAndAnalyzeData()">Load & Analyze Lead Data</button>
            <div id="data-loading-results"></div>
            <div class="metrics-grid" id="basic-metrics"></div>
        </div>

        <div class="test-section">
            <h2>🔍 Date Filtering Test</h2>
            <div class="filter-controls">
                <label>Test Date Filter:</label>
                <select id="test-date-filter">
                    <option value="all">All Time</option>
                    <option value="last-14">Last 14 Days</option>
                    <option value="last-30">Last 30 Days</option>
                    <option value="last-60">Last 60 Days</option>
                    <option value="last-90">Last 90 Days</option>
                </select>
                <button onclick="applyTestFilter()">Apply Filter & Test Charts</button>
            </div>
            <div id="filter-test-results"></div>
        </div>

        <div class="test-section">
            <h2>📈 Lead Volume Chart Data Validation</h2>
            <button onclick="validateLeadVolumeChart()">Validate Time Series Data</button>
            <div id="volume-chart-results"></div>
            <div class="chart-data-section" id="volume-chart-data"></div>
        </div>

        <div class="test-section">
            <h2>🥧 Lead Sources Chart Validation</h2>
            <button onclick="validateSourceChart()">Validate Source Distribution</button>
            <div id="source-chart-results"></div>
            <div class="chart-data-section" id="source-chart-data"></div>
        </div>

        <div class="test-section">
            <h2>📊 Channel Distribution Chart Validation</h2>
            <button onclick="validateChannelChart()">Validate Channel Data</button>
            <div id="channel-chart-results"></div>
            <div class="chart-data-section" id="channel-chart-data"></div>
        </div>

        <div class="test-section">
            <h2>📋 Statistics Cards Validation</h2>
            <button onclick="validateStatisticsCards()">Validate KPI Cards</button>
            <div id="stats-validation-results"></div>
            <table class="comparison-table" id="stats-comparison-table" style="display: none;">
                <thead>
                    <tr>
                        <th>Metric</th>
                        <th>Expected Value</th>
                        <th>Chart Function Result</th>
                        <th>Manual Calculation</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody id="stats-comparison-tbody"></tbody>
            </table>
        </div>

        <div class="test-section">
            <h2>🎯 Overall Accuracy Assessment</h2>
            <button onclick="generateAccuracyReport()">Generate Complete Accuracy Report</button>
            <div id="accuracy-report"></div>
            <div class="accuracy-score" id="overall-score"></div>
        </div>
    </div>

    <script>
        let testLeadData = [];
        let filteredTestData = [];
        let testDateRange = null;
        let chartValidationResults = {
            volumeChart: null,
            sourceChart: null,
            channelChart: null,
            statisticsCards: null
        };

        function logResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            container.appendChild(resultDiv);
        }

        async function loadAndAnalyzeData() {
            logResult('data-loading-results', 'Loading Lead data from Airtable...', 'info');
            
            try {
                const response = await fetch('/api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblcdFVUC3zJrbmNf&maxRecords=2000');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                testLeadData = Array.isArray(data) ? data : (data.records || []);
                filteredTestData = [...testLeadData]; // Start with all data
                
                logResult('data-loading-results', `✅ Successfully loaded ${testLeadData.length} Lead records`, 'success');
                
                // Analyze date range
                const dates = testLeadData.map(record => record['Date Created']).filter(Boolean);
                const parsedDates = dates.map(dateStr => {
                    if (dateStr.match(/^\d{4}-\d{2}-\d{2}$/)) {
                        return new Date(dateStr + 'T00:00:00');
                    }
                    return new Date(dateStr);
                }).filter(date => !isNaN(date));

                if (parsedDates.length > 0) {
                    const minDate = new Date(Math.min(...parsedDates));
                    const maxDate = new Date(Math.max(...parsedDates));
                    
                    testDateRange = {
                        min: minDate,
                        max: maxDate,
                        minStr: minDate.toISOString().split('T')[0],
                        maxStr: maxDate.toISOString().split('T')[0]
                    };

                    logResult('data-loading-results', `📅 Date Range: ${testDateRange.minStr} to ${testDateRange.maxStr}`, 'info');
                    
                    // Update basic metrics
                    updateBasicMetrics();
                }
                
            } catch (error) {
                logResult('data-loading-results', `❌ Error loading data: ${error.message}`, 'error');
            }
        }

        function updateBasicMetrics() {
            const totalLeads = filteredTestData.length;
            const trafficSources = {};
            const channels = {};

            filteredTestData.forEach(record => {
                const source = record['Traffic Source'] || 'Other';
                const channel = record['Channel'] || 'Unknown';
                
                trafficSources[source] = (trafficSources[source] || 0) + 1;
                channels[channel] = (channels[channel] || 0) + 1;
            });

            const googlePaid = trafficSources['Google Paid'] || 0;
            const googleOrganic = trafficSources['Google Organic'] || 0;
            const meta = trafficSources['Meta'] || 0;

            document.getElementById('basic-metrics').innerHTML = `
                <div class="metric-card">
                    <div class="metric-value">${totalLeads}</div>
                    <div class="metric-label">Total Leads</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${googlePaid + googleOrganic}</div>
                    <div class="metric-label">Google Leads</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${meta}</div>
                    <div class="metric-label">Meta Leads</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${Object.keys(trafficSources).length}</div>
                    <div class="metric-label">Traffic Sources</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${Object.keys(channels).length}</div>
                    <div class="metric-label">Channels</div>
                </div>
            `;
        }

        function applyTestFilter() {
            if (!testLeadData.length || !testDateRange) {
                logResult('filter-test-results', '❌ Please load data first', 'error');
                return;
            }

            const filterValue = document.getElementById('test-date-filter').value;
            logResult('filter-test-results', `🔍 Applying filter: ${filterValue}`, 'info');

            if (filterValue === 'all') {
                filteredTestData = [...testLeadData];
            } else {
                const daysToSubtract = {
                    'last-14': 14,
                    'last-30': 30,
                    'last-60': 60,
                    'last-90': 90
                }[filterValue] || 30;

                const latestDate = new Date(testDateRange.max);
                const startDate = new Date(latestDate);
                startDate.setDate(latestDate.getDate() - daysToSubtract + 1);

                const startDateStr = startDate.toISOString().split('T')[0];
                const endDateStr = latestDate.toISOString().split('T')[0];

                filteredTestData = testLeadData.filter(record => {
                    const recordDateStr = record['Date Created'];
                    if (!recordDateStr) return false;
                    
                    let normalizedDateStr = recordDateStr;
                    if (recordDateStr.includes('T')) {
                        normalizedDateStr = recordDateStr.split('T')[0];
                    }
                    
                    return normalizedDateStr >= startDateStr && normalizedDateStr <= endDateStr;
                });

                logResult('filter-test-results', `📊 Date range: ${startDateStr} to ${endDateStr}`, 'info');
            }

            logResult('filter-test-results', `✅ Filtered to ${filteredTestData.length} records`, 'success');
            updateBasicMetrics();
        }

        // Replicate the getTimeSeriesData function from your web app
        function getTimeSeriesDataTest(data) {
            const dateSourceMap = {};

            data.forEach(entry => {
                const date = entry['Date Created'] ? new Date(entry['Date Created']) : null;
                if (!date) return;

                const dateKey = date.toISOString().split('T')[0];
                const source = entry['Traffic Source'] || 'Other';

                if (!dateSourceMap[dateKey]) {
                    dateSourceMap[dateKey] = {};
                }

                dateSourceMap[dateKey][source] = (dateSourceMap[dateKey][source] || 0) + 1;
            });

            // Convert to chart format
            const dates = Object.keys(dateSourceMap).sort();
            const sources = ['Google Paid', 'Google Organic', 'Meta', 'Other'];

            return {
                labels: dates,
                datasets: sources.map(source => ({
                    label: source,
                    data: dates.map(date => dateSourceMap[date][source] || 0)
                }))
            };
        }

        // Replicate the getSourceData function from your web app
        function getSourceDataTest(data) {
            const sourceCounts = {
                'Google Paid': 0,
                'Google Organic': 0,
                'Meta': 0,
                'Other': 0
            };

            data.forEach(entry => {
                const source = entry['Traffic Source'] || 'Other';
                if (sourceCounts.hasOwnProperty(source)) {
                    sourceCounts[source]++;
                } else {
                    sourceCounts['Other']++;
                }
            });

            return {
                labels: Object.keys(sourceCounts),
                datasets: [{
                    data: Object.values(sourceCounts),
                    backgroundColor: ['#e91e63', '#ff5722', '#4caf50', '#2196f3']
                }]
            };
        }

        // Replicate the getChannelData function from your web app
        function getChannelDataTest(data) {
            const channelCounts = {
                'Call': 0,
                'Email': 0,
                'SMS': 0,
                'FB': 0,
                'IG': 0,
                'Other': 0
            };

            data.forEach(entry => {
                const channel = entry['Channel'] || 'Other';
                if (channelCounts.hasOwnProperty(channel)) {
                    channelCounts[channel]++;
                } else {
                    channelCounts['Other']++;
                }
            });

            return {
                labels: Object.keys(channelCounts),
                datasets: [{
                    label: 'Lead Count',
                    data: Object.values(channelCounts),
                    backgroundColor: ['#e91e63', '#ff5722', '#4caf50', '#2196f3', '#9c27b0', '#607d8b']
                }]
            };
        }

        // Replicate the collectStatistics function from your web app
        function collectStatisticsTest(data) {
            const leadCount = data.length;
            const trafficSourceCounts = {};
            const channelCounts = {};

            let googleLeads = 0;
            let metaLeads = 0;

            data.forEach(entry => {
                const source = entry['Traffic Source'] || 'Other';
                const channel = entry['Channel'] || 'Other';

                trafficSourceCounts[source] = (trafficSourceCounts[source] || 0) + 1;
                channelCounts[channel] = (channelCounts[channel] || 0) + 1;

                // Count Google and Meta leads
                const sourceLower = source.toLowerCase();
                if (sourceLower.includes('google') || sourceLower.includes('adwords') || sourceLower.includes('gads')) {
                    googleLeads++;
                } else if (sourceLower.includes('facebook') || sourceLower.includes('meta') || sourceLower.includes('fb')) {
                    metaLeads++;
                }
            });

            const otherLeads = leadCount - googleLeads - metaLeads;

            return {
                leadCount,
                trafficSourceCounts,
                channelCounts,
                googleLeads,
                metaLeads,
                otherLeads,
                conversionRate: 0, // Would need POS data for actual calculation
                avgLeadValue: 0    // Would need revenue data for actual calculation
            };
        }

        function validateLeadVolumeChart() {
            if (!filteredTestData.length) {
                logResult('volume-chart-results', '❌ Please load and filter data first', 'error');
                return;
            }

            logResult('volume-chart-results', '🔍 Validating Lead Volume Chart data...', 'info');

            const timeSeriesData = getTimeSeriesDataTest(filteredTestData);

            // Validate data structure
            if (!timeSeriesData.labels || !timeSeriesData.datasets) {
                logResult('volume-chart-results', '❌ Invalid time series data structure', 'error');
                chartValidationResults.volumeChart = false;
                return;
            }

            // Calculate totals for validation
            const totalsBySource = {};
            timeSeriesData.datasets.forEach(dataset => {
                const total = dataset.data.reduce((sum, value) => sum + value, 0);
                totalsBySource[dataset.label] = total;
            });

            const grandTotal = Object.values(totalsBySource).reduce((sum, value) => sum + value, 0);

            logResult('volume-chart-results', `✅ Time series data generated successfully`, 'success');
            logResult('volume-chart-results', `📊 Date range: ${timeSeriesData.labels.length} days`, 'info');
            logResult('volume-chart-results', `📈 Total leads in chart: ${grandTotal}`, 'info');
            logResult('volume-chart-results', `📋 Filtered data count: ${filteredTestData.length}`, 'info');

            // Validate totals match
            if (grandTotal === filteredTestData.length) {
                logResult('volume-chart-results', '✅ Chart totals match filtered data count', 'success');
                chartValidationResults.volumeChart = true;
            } else {
                logResult('volume-chart-results', `❌ Chart totals (${grandTotal}) don't match filtered data (${filteredTestData.length})`, 'error');
                chartValidationResults.volumeChart = false;
            }

            // Display detailed data
            document.getElementById('volume-chart-data').innerHTML = `
                <strong>Time Series Data Structure:</strong><br>
                Labels (dates): ${timeSeriesData.labels.length} entries<br>
                First date: ${timeSeriesData.labels[0] || 'N/A'}<br>
                Last date: ${timeSeriesData.labels[timeSeriesData.labels.length - 1] || 'N/A'}<br><br>
                <strong>Totals by Source:</strong><br>
                ${Object.entries(totalsBySource).map(([source, total]) => `${source}: ${total}`).join('<br>')}<br><br>
                <strong>Grand Total:</strong> ${grandTotal}<br>
                <strong>Expected Total:</strong> ${filteredTestData.length}
            `;
        }

        function validateSourceChart() {
            if (!filteredTestData.length) {
                logResult('source-chart-results', '❌ Please load and filter data first', 'error');
                return;
            }

            logResult('source-chart-results', '🔍 Validating Source Chart data...', 'info');

            const sourceData = getSourceDataTest(filteredTestData);

            // Validate data structure
            if (!sourceData.labels || !sourceData.datasets || !sourceData.datasets[0]) {
                logResult('source-chart-results', '❌ Invalid source data structure', 'error');
                chartValidationResults.sourceChart = false;
                return;
            }

            const sourceDataset = sourceData.datasets[0];
            const totalInChart = sourceDataset.data.reduce((sum, value) => sum + value, 0);

            logResult('source-chart-results', `✅ Source chart data generated successfully`, 'success');
            logResult('source-chart-results', `📊 Sources: ${sourceData.labels.length}`, 'info');
            logResult('source-chart-results', `📈 Total leads in chart: ${totalInChart}`, 'info');
            logResult('source-chart-results', `📋 Filtered data count: ${filteredTestData.length}`, 'info');

            // Validate totals match
            if (totalInChart === filteredTestData.length) {
                logResult('source-chart-results', '✅ Chart totals match filtered data count', 'success');
                chartValidationResults.sourceChart = true;
            } else {
                logResult('source-chart-results', `❌ Chart totals (${totalInChart}) don't match filtered data (${filteredTestData.length})`, 'error');
                chartValidationResults.sourceChart = false;
            }

            // Display detailed data
            const sourceBreakdown = sourceData.labels.map((label, index) =>
                `${label}: ${sourceDataset.data[index]}`
            ).join('<br>');

            document.getElementById('source-chart-data').innerHTML = `
                <strong>Source Distribution:</strong><br>
                ${sourceBreakdown}<br><br>
                <strong>Total in Chart:</strong> ${totalInChart}<br>
                <strong>Expected Total:</strong> ${filteredTestData.length}
            `;
        }

        function validateChannelChart() {
            if (!filteredTestData.length) {
                logResult('channel-chart-results', '❌ Please load and filter data first', 'error');
                return;
            }

            logResult('channel-chart-results', '🔍 Validating Channel Chart data...', 'info');

            const channelData = getChannelDataTest(filteredTestData);

            // Validate data structure
            if (!channelData.labels || !channelData.datasets || !channelData.datasets[0]) {
                logResult('channel-chart-results', '❌ Invalid channel data structure', 'error');
                chartValidationResults.channelChart = false;
                return;
            }

            const channelDataset = channelData.datasets[0];
            const totalInChart = channelDataset.data.reduce((sum, value) => sum + value, 0);

            logResult('channel-chart-results', `✅ Channel chart data generated successfully`, 'success');
            logResult('channel-chart-results', `📊 Channels: ${channelData.labels.length}`, 'info');
            logResult('channel-chart-results', `📈 Total leads in chart: ${totalInChart}`, 'info');
            logResult('channel-chart-results', `📋 Filtered data count: ${filteredTestData.length}`, 'info');

            // Validate totals match
            if (totalInChart === filteredTestData.length) {
                logResult('channel-chart-results', '✅ Chart totals match filtered data count', 'success');
                chartValidationResults.channelChart = true;
            } else {
                logResult('channel-chart-results', `❌ Chart totals (${totalInChart}) don't match filtered data (${filteredTestData.length})`, 'error');
                chartValidationResults.channelChart = false;
            }

            // Display detailed data
            const channelBreakdown = channelData.labels.map((label, index) =>
                `${label}: ${channelDataset.data[index]}`
            ).join('<br>');

            document.getElementById('channel-chart-data').innerHTML = `
                <strong>Channel Distribution:</strong><br>
                ${channelBreakdown}<br><br>
                <strong>Total in Chart:</strong> ${totalInChart}<br>
                <strong>Expected Total:</strong> ${filteredTestData.length}
            `;
        }

        function validateStatisticsCards() {
            if (!filteredTestData.length) {
                logResult('stats-validation-results', '❌ Please load and filter data first', 'error');
                return;
            }

            logResult('stats-validation-results', '🔍 Validating Statistics Cards...', 'info');

            const stats = collectStatisticsTest(filteredTestData);

            // Manual calculations for verification
            const manualCounts = {
                totalLeads: filteredTestData.length,
                googlePaid: 0,
                googleOrganic: 0,
                meta: 0,
                other: 0
            };

            filteredTestData.forEach(record => {
                const source = record['Traffic Source'] || 'Other';
                switch(source) {
                    case 'Google Paid':
                        manualCounts.googlePaid++;
                        break;
                    case 'Google Organic':
                        manualCounts.googleOrganic++;
                        break;
                    case 'Meta':
                        manualCounts.meta++;
                        break;
                    default:
                        manualCounts.other++;
                        break;
                }
            });

            const manualGoogleTotal = manualCounts.googlePaid + manualCounts.googleOrganic;

            // Comparison data
            const comparisons = [
                {
                    metric: 'Total Leads',
                    expected: manualCounts.totalLeads,
                    chartFunction: stats.leadCount,
                    manual: manualCounts.totalLeads
                },
                {
                    metric: 'Google Leads',
                    expected: manualGoogleTotal,
                    chartFunction: stats.googleLeads,
                    manual: manualGoogleTotal
                },
                {
                    metric: 'Google Paid',
                    expected: manualCounts.googlePaid,
                    chartFunction: stats.trafficSourceCounts['Google Paid'] || 0,
                    manual: manualCounts.googlePaid
                },
                {
                    metric: 'Google Organic',
                    expected: manualCounts.googleOrganic,
                    chartFunction: stats.trafficSourceCounts['Google Organic'] || 0,
                    manual: manualCounts.googleOrganic
                },
                {
                    metric: 'Meta Leads',
                    expected: manualCounts.meta,
                    chartFunction: stats.metaLeads,
                    manual: manualCounts.meta
                },
                {
                    metric: 'Other Sources',
                    expected: manualCounts.other,
                    chartFunction: stats.otherLeads,
                    manual: manualCounts.other
                }
            ];

            // Populate comparison table
            const tbody = document.getElementById('stats-comparison-tbody');
            tbody.innerHTML = '';

            let allMatch = true;

            comparisons.forEach(comp => {
                const matches = comp.expected === comp.chartFunction && comp.chartFunction === comp.manual;
                if (!matches) allMatch = false;

                const row = tbody.insertRow();
                row.innerHTML = `
                    <td>${comp.metric}</td>
                    <td>${comp.expected}</td>
                    <td>${comp.chartFunction}</td>
                    <td>${comp.manual}</td>
                    <td style="color: ${matches ? '#4caf50' : '#f44336'}">${matches ? '✅ Match' : '❌ Mismatch'}</td>
                `;
            });

            document.getElementById('stats-comparison-table').style.display = 'table';

            if (allMatch) {
                logResult('stats-validation-results', '✅ All statistics cards match expected values', 'success');
                chartValidationResults.statisticsCards = true;
            } else {
                logResult('stats-validation-results', '❌ Some statistics cards have mismatched values', 'error');
                chartValidationResults.statisticsCards = false;
            }

            logResult('stats-validation-results', `📊 Validated ${comparisons.length} metrics`, 'info');
        }

        function generateAccuracyReport() {
            logResult('accuracy-report', '📋 Generating comprehensive accuracy report...', 'info');

            const results = chartValidationResults;
            const totalTests = Object.keys(results).length;
            const passedTests = Object.values(results).filter(result => result === true).length;
            const failedTests = Object.values(results).filter(result => result === false).length;
            const notRunTests = Object.values(results).filter(result => result === null).length;

            let accuracyScore = 0;
            let scoreClass = 'poor';
            let scoreText = 'Poor';

            if (passedTests === totalTests && notRunTests === 0) {
                accuracyScore = 100;
                scoreClass = 'excellent';
                scoreText = 'Excellent';
            } else if (passedTests >= totalTests * 0.8) {
                accuracyScore = Math.round((passedTests / totalTests) * 100);
                scoreClass = 'good';
                scoreText = 'Good';
            } else if (passedTests > 0) {
                accuracyScore = Math.round((passedTests / totalTests) * 100);
                scoreClass = 'poor';
                scoreText = 'Needs Improvement';
            }

            // Detailed report
            const reportHTML = `
                <div class="test-result info">
                    <strong>📊 Test Summary:</strong><br>
                    • Total Tests: ${totalTests}<br>
                    • Passed: ${passedTests}<br>
                    • Failed: ${failedTests}<br>
                    • Not Run: ${notRunTests}
                </div>

                <div class="test-result ${passedTests === totalTests ? 'success' : 'warning'}">
                    <strong>📈 Chart Validation Results:</strong><br>
                    • Lead Volume Chart: ${results.volumeChart === true ? '✅ Pass' : results.volumeChart === false ? '❌ Fail' : '⏳ Not Run'}<br>
                    • Source Chart: ${results.sourceChart === true ? '✅ Pass' : results.sourceChart === false ? '❌ Fail' : '⏳ Not Run'}<br>
                    • Channel Chart: ${results.channelChart === true ? '✅ Pass' : results.channelChart === false ? '❌ Fail' : '⏳ Not Run'}<br>
                    • Statistics Cards: ${results.statisticsCards === true ? '✅ Pass' : results.statisticsCards === false ? '❌ Fail' : '⏳ Not Run'}
                </div>

                ${passedTests === totalTests && notRunTests === 0 ? `
                <div class="test-result success">
                    <strong>🎉 All Tests Passed!</strong><br>
                    Your Leads Report charts and visualizations are displaying accurate data.
                    The filtering system is working correctly and all chart data matches the expected values.
                </div>
                ` : `
                <div class="test-result warning">
                    <strong>⚠️ Action Required:</strong><br>
                    ${failedTests > 0 ? `${failedTests} test(s) failed. Please review the chart implementations.` : ''}
                    ${notRunTests > 0 ? `${notRunTests} test(s) not run. Please run all validation tests.` : ''}
                </div>
                `}

                <div class="test-result info">
                    <strong>📋 Recommendations:</strong><br>
                    • Compare these results with your actual web app<br>
                    • Test different date filters to ensure consistency<br>
                    • Verify that chart totals always match filtered data counts<br>
                    • Check that source and channel distributions are accurate
                </div>
            `;

            document.getElementById('accuracy-report').innerHTML = reportHTML;

            const scoreElement = document.getElementById('overall-score');
            scoreElement.className = `accuracy-score ${scoreClass}`;
            scoreElement.textContent = `Overall Accuracy Score: ${accuracyScore}% (${scoreText})`;

            logResult('accuracy-report', '✅ Accuracy report generated successfully', 'success');
        }

        // Auto-load data on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                loadAndAnalyzeData();
            }, 1000);
        });
    </script>
</body>
</html>
