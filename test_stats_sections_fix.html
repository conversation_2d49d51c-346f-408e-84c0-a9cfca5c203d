<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Stats Sections Fix Test</title>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #121212;
            color: #ffffff;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: #1e1e1e;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        h1 {
            color: #e91e63;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            background: #2a2a2a;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #e91e63;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 6px;
            border-left: 4px solid;
        }
        .test-result.success {
            background: rgba(76, 175, 80, 0.1);
            border-color: #4caf50;
            color: #4caf50;
        }
        .test-result.error {
            background: rgba(244, 67, 54, 0.1);
            border-color: #f44336;
            color: #f44336;
        }
        .test-result.info {
            background: rgba(33, 150, 243, 0.1);
            border-color: #2196f3;
            color: #2196f3;
        }
        button {
            background: linear-gradient(135deg, #e91e63, #ff5722);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            margin: 10px 10px 10px 0;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
        }
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .comparison-card {
            background: #333;
            padding: 15px;
            border-radius: 8px;
        }
        .comparison-card h4 {
            color: #e91e63;
            margin-top: 0;
        }
        .metric-value {
            font-size: 18px;
            font-weight: bold;
            color: #4caf50;
        }
        .fix-status {
            font-size: 18px;
            font-weight: bold;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            margin: 20px 0;
        }
        .fix-status.fixed {
            background: rgba(76, 175, 80, 0.2);
            color: #4caf50;
            border: 2px solid #4caf50;
        }
        .fix-status.broken {
            background: rgba(244, 67, 54, 0.2);
            color: #f44336;
            border: 2px solid #f44336;
        }
        .filter-controls {
            margin: 20px 0;
        }
        .filter-controls select {
            background: #333;
            color: white;
            border: 1px solid #555;
            padding: 8px 12px;
            border-radius: 4px;
            margin: 0 10px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Stats Sections Fix Test</h1>
        <p>This test verifies that the Traffic Sources, Channels, and Conversion Events sections now update correctly when date filters are applied.</p>

        <div class="test-section">
            <h2>📊 Data Loading</h2>
            <button onclick="loadTestData()">Load Test Data</button>
            <div id="data-loading-results"></div>
        </div>

        <div class="test-section">
            <h2>🔍 Filter Testing</h2>
            <div class="filter-controls">
                <label>Test Filter:</label>
                <select id="test-filter">
                    <option value="all">All Time</option>
                    <option value="last-14">Last 14 Days</option>
                    <option value="last-30">Last 30 Days</option>
                    <option value="last-60">Last 60 Days</option>
                </select>
                <button onclick="testFilterUpdate()">Apply Filter & Test Sections</button>
            </div>
            <div id="filter-test-results"></div>
        </div>

        <div class="test-section">
            <h2>📈 Traffic Sources Section Test</h2>
            <button onclick="testTrafficSourcesSection()">Test Traffic Sources Update</button>
            <div id="traffic-sources-results"></div>
            <div class="comparison-grid">
                <div class="comparison-card">
                    <h4>Expected Counts</h4>
                    <div id="expected-traffic-sources"></div>
                </div>
                <div class="comparison-card">
                    <h4>Function Output</h4>
                    <div id="function-traffic-sources"></div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>📊 Channels Section Test</h2>
            <button onclick="testChannelsSection()">Test Channels Update</button>
            <div id="channels-results"></div>
            <div class="comparison-grid">
                <div class="comparison-card">
                    <h4>Expected Counts</h4>
                    <div id="expected-channels"></div>
                </div>
                <div class="comparison-card">
                    <h4>Function Output</h4>
                    <div id="function-channels"></div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>🎯 Conversion Events Section Test</h2>
            <button onclick="testConversionEventsSection()">Test Conversion Events Update</button>
            <div id="conversion-events-results"></div>
            <div class="comparison-grid">
                <div class="comparison-card">
                    <h4>Expected Counts</h4>
                    <div id="expected-conversion-events"></div>
                </div>
                <div class="comparison-card">
                    <h4>Function Output</h4>
                    <div id="function-conversion-events"></div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>✅ Overall Fix Status</h2>
            <button onclick="generateOverallStatus()">Generate Fix Status Report</button>
            <div id="overall-results"></div>
            <div class="fix-status" id="final-fix-status"></div>
        </div>

        <div class="test-section">
            <h2>📋 Instructions for Web App Testing</h2>
            <div class="test-result info">
                <strong>🔍 How to Test Your Web App:</strong><br>
                1. Open your main web application<br>
                2. Go to the Leads Report tab<br>
                3. Look at the "Traffic Sources (How leads found us)" section<br>
                4. Look at the "Channels (How leads contacted us)" section<br>
                5. Look at the "Conversion Events" section<br>
                6. Apply different date filters (Last 14 Days, Last 30 Days)<br>
                7. Watch these sections update - they should now change when you filter!<br>
                8. Verify the numbers match your filtered data
            </div>
        </div>
    </div>

    <script>
        let testData = [];
        let filteredTestData = [];

        function logResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            container.appendChild(resultDiv);
        }

        async function loadTestData() {
            logResult('data-loading-results', 'Loading test data...', 'info');
            
            try {
                const response = await fetch('/api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblcdFVUC3zJrbmNf&maxRecords=2000');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                testData = Array.isArray(data) ? data : (data.records || []);
                filteredTestData = [...testData]; // Start with all data
                
                logResult('data-loading-results', `✅ Successfully loaded ${testData.length} records`, 'success');
                
            } catch (error) {
                logResult('data-loading-results', `❌ Error loading data: ${error.message}`, 'error');
            }
        }

        function testFilterUpdate() {
            if (!testData.length) {
                logResult('filter-test-results', '❌ Please load test data first', 'error');
                return;
            }

            const filterValue = document.getElementById('test-filter').value;
            logResult('filter-test-results', `🔍 Testing filter: ${filterValue}`, 'info');

            // Apply filter (simplified version)
            if (filterValue === 'all') {
                filteredTestData = [...testData];
            } else {
                const daysToSubtract = {
                    'last-14': 14,
                    'last-30': 30,
                    'last-60': 60
                }[filterValue] || 30;

                const today = new Date();
                const cutoffDate = new Date();
                cutoffDate.setDate(today.getDate() - daysToSubtract);

                filteredTestData = testData.filter(record => {
                    const recordDate = new Date(record['Date Created']);
                    return recordDate >= cutoffDate;
                });
            }

            logResult('filter-test-results', `✅ Filtered to ${filteredTestData.length} records`, 'success');
            logResult('filter-test-results', `📊 Now test the individual sections to see if they use filtered data`, 'info');
        }

        // Replicate the collectStatistics function
        function collectStatisticsTest(data) {
            const stats = {
                trafficSourceCounts: {},
                channelCounts: {},
                conversionEventCounts: {}
            };

            data.forEach(entry => {
                // Count traffic sources
                const source = entry['Traffic Source'] || 'Unknown';
                stats.trafficSourceCounts[source] = (stats.trafficSourceCounts[source] || 0) + 1;

                // Count channels
                const channel = entry['Channel'] || 'Unknown';
                stats.channelCounts[channel] = (stats.channelCounts[channel] || 0) + 1;

                // Count conversion events
                const convEvent = entry['Conversion Event'] || 'Unknown';
                stats.conversionEventCounts[convEvent] = (stats.conversionEventCounts[convEvent] || 0) + 1;
            });

            return stats;
        }

        function testTrafficSourcesSection() {
            if (!filteredTestData.length) {
                logResult('traffic-sources-results', '❌ Please load and filter data first', 'error');
                return;
            }

            logResult('traffic-sources-results', '🔍 Testing Traffic Sources section...', 'info');

            const stats = collectStatisticsTest(filteredTestData);
            
            // Expected counts (manual calculation)
            const expectedCounts = {};
            filteredTestData.forEach(record => {
                const source = record['Traffic Source'] || 'Unknown';
                expectedCounts[source] = (expectedCounts[source] || 0) + 1;
            });

            // Display results
            document.getElementById('expected-traffic-sources').innerHTML = Object.entries(expectedCounts)
                .map(([source, count]) => `<div>${source}: <span class="metric-value">${count}</span></div>`)
                .join('');

            document.getElementById('function-traffic-sources').innerHTML = Object.entries(stats.trafficSourceCounts)
                .map(([source, count]) => `<div>${source}: <span class="metric-value">${count}</span></div>`)
                .join('');

            // Check if they match
            const matches = JSON.stringify(expectedCounts) === JSON.stringify(stats.trafficSourceCounts);
            logResult('traffic-sources-results', 
                matches ? '✅ Traffic Sources section would update correctly!' : '❌ Traffic Sources section has issues',
                matches ? 'success' : 'error'
            );

            return matches;
        }

        function testChannelsSection() {
            if (!filteredTestData.length) {
                logResult('channels-results', '❌ Please load and filter data first', 'error');
                return;
            }

            logResult('channels-results', '🔍 Testing Channels section...', 'info');

            const stats = collectStatisticsTest(filteredTestData);
            
            // Expected counts (manual calculation)
            const expectedCounts = {};
            filteredTestData.forEach(record => {
                const channel = record['Channel'] || 'Unknown';
                expectedCounts[channel] = (expectedCounts[channel] || 0) + 1;
            });

            // Display results
            document.getElementById('expected-channels').innerHTML = Object.entries(expectedCounts)
                .map(([channel, count]) => `<div>${channel}: <span class="metric-value">${count}</span></div>`)
                .join('');

            document.getElementById('function-channels').innerHTML = Object.entries(stats.channelCounts)
                .map(([channel, count]) => `<div>${channel}: <span class="metric-value">${count}</span></div>`)
                .join('');

            // Check if they match
            const matches = JSON.stringify(expectedCounts) === JSON.stringify(stats.channelCounts);
            logResult('channels-results', 
                matches ? '✅ Channels section would update correctly!' : '❌ Channels section has issues',
                matches ? 'success' : 'error'
            );

            return matches;
        }

        function testConversionEventsSection() {
            if (!filteredTestData.length) {
                logResult('conversion-events-results', '❌ Please load and filter data first', 'error');
                return;
            }

            logResult('conversion-events-results', '🔍 Testing Conversion Events section...', 'info');

            const stats = collectStatisticsTest(filteredTestData);
            
            // Expected counts (manual calculation)
            const expectedCounts = {};
            filteredTestData.forEach(record => {
                const convEvent = record['Conversion Event'] || 'Unknown';
                expectedCounts[convEvent] = (expectedCounts[convEvent] || 0) + 1;
            });

            // Display results
            document.getElementById('expected-conversion-events').innerHTML = Object.entries(expectedCounts)
                .map(([event, count]) => `<div>${event}: <span class="metric-value">${count}</span></div>`)
                .join('');

            document.getElementById('function-conversion-events').innerHTML = Object.entries(stats.conversionEventCounts)
                .map(([event, count]) => `<div>${event}: <span class="metric-value">${count}</span></div>`)
                .join('');

            // Check if they match
            const matches = JSON.stringify(expectedCounts) === JSON.stringify(stats.conversionEventCounts);
            logResult('conversion-events-results', 
                matches ? '✅ Conversion Events section would update correctly!' : '❌ Conversion Events section has issues',
                matches ? 'success' : 'error'
            );

            return matches;
        }

        function generateOverallStatus() {
            logResult('overall-results', '📋 Generating overall fix status...', 'info');

            const trafficSourcesWorking = testTrafficSourcesSection();
            const channelsWorking = testChannelsSection();
            const conversionEventsWorking = testConversionEventsSection();

            const allWorking = trafficSourcesWorking && channelsWorking && conversionEventsWorking;

            const statusElement = document.getElementById('final-fix-status');
            if (allWorking) {
                statusElement.className = 'fix-status fixed';
                statusElement.textContent = '✅ ALL SECTIONS FIXED! They will now update with filters.';
                logResult('overall-results', '🎉 SUCCESS! All stats sections are now working correctly.', 'success');
            } else {
                statusElement.className = 'fix-status broken';
                statusElement.textContent = '❌ Some sections still have issues';
                logResult('overall-results', '⚠️ Some sections still need work.', 'error');
            }

            logResult('overall-results', `📊 Traffic Sources: ${trafficSourcesWorking ? '✅ Working' : '❌ Broken'}`, 
                trafficSourcesWorking ? 'success' : 'error');
            logResult('overall-results', `📊 Channels: ${channelsWorking ? '✅ Working' : '❌ Broken'}`, 
                channelsWorking ? 'success' : 'error');
            logResult('overall-results', `📊 Conversion Events: ${conversionEventsWorking ? '✅ Working' : '❌ Broken'}`, 
                conversionEventsWorking ? 'success' : 'error');
        }

        // Auto-load data on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                loadTestData();
            }, 1000);
        });
    </script>
</body>
</html>
