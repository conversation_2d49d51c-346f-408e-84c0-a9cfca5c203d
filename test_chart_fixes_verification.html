<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Chart Fixes Verification Test</title>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #121212;
            color: #ffffff;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: #1e1e1e;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        h1 {
            color: #e91e63;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            background: #2a2a2a;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #e91e63;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 6px;
            border-left: 4px solid;
        }
        .test-result.success {
            background: rgba(76, 175, 80, 0.1);
            border-color: #4caf50;
            color: #4caf50;
        }
        .test-result.error {
            background: rgba(244, 67, 54, 0.1);
            border-color: #f44336;
            color: #f44336;
        }
        .test-result.info {
            background: rgba(33, 150, 243, 0.1);
            border-color: #2196f3;
            color: #2196f3;
        }
        .test-result.warning {
            background: rgba(255, 193, 7, 0.1);
            border-color: #ffc107;
            color: #ffc107;
        }
        button {
            background: linear-gradient(135deg, #e91e63, #ff5722);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            margin: 10px 10px 10px 0;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .metric-card {
            background: #333;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #e91e63;
        }
        .metric-label {
            font-size: 14px;
            color: #aaa;
            margin-top: 5px;
        }
        .fix-status {
            font-size: 18px;
            font-weight: bold;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            margin: 20px 0;
        }
        .fix-status.fixed {
            background: rgba(76, 175, 80, 0.2);
            color: #4caf50;
            border: 2px solid #4caf50;
        }
        .fix-status.broken {
            background: rgba(244, 67, 54, 0.2);
            color: #f44336;
            border: 2px solid #f44336;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 14px;
        }
        .comparison-table th,
        .comparison-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #444;
        }
        .comparison-table th {
            background: #333;
            color: #e91e63;
            font-weight: 600;
        }
        .comparison-table tr:nth-child(even) {
            background: rgba(255, 255, 255, 0.02);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Chart Fixes Verification Test</h1>
        <p>This test verifies that the chart accuracy issues have been resolved after fixing the function names and data flow.</p>

        <div class="test-section">
            <h2>📊 Data Loading & Setup</h2>
            <button onclick="loadTestData()">Load Test Data</button>
            <div id="data-loading-results"></div>
            <div class="metrics-grid" id="basic-metrics"></div>
        </div>

        <div class="test-section">
            <h2>🔍 Chart Function Verification</h2>
            <button onclick="testChartFunctions()">Test Chart Data Functions</button>
            <div id="chart-function-results"></div>
            <table class="comparison-table" id="function-comparison-table" style="display: none;">
                <thead>
                    <tr>
                        <th>Chart Function</th>
                        <th>Expected Total</th>
                        <th>Actual Total</th>
                        <th>Status</th>
                        <th>Details</th>
                    </tr>
                </thead>
                <tbody id="function-comparison-tbody"></tbody>
            </table>
        </div>

        <div class="test-section">
            <h2>📈 Lead Volume Chart (Time Series) Test</h2>
            <button onclick="testLeadVolumeChart()">Test Lead Volume Chart</button>
            <div id="volume-chart-results"></div>
            <div class="fix-status" id="volume-chart-status"></div>
        </div>

        <div class="test-section">
            <h2>🥧 Source Chart Test</h2>
            <button onclick="testSourceChart()">Test Source Chart</button>
            <div id="source-chart-results"></div>
            <div class="fix-status" id="source-chart-status"></div>
        </div>

        <div class="test-section">
            <h2>📊 Channel Chart Test</h2>
            <button onclick="testChannelChart()">Test Channel Chart</button>
            <div id="channel-chart-results"></div>
            <div class="fix-status" id="channel-chart-status"></div>
        </div>

        <div class="test-section">
            <h2>🎯 Overall Fix Status</h2>
            <button onclick="generateFixReport()">Generate Fix Verification Report</button>
            <div id="fix-report"></div>
            <div class="fix-status" id="overall-fix-status"></div>
        </div>
    </div>

    <script>
        let testData = [];
        let testResults = {
            volumeChart: null,
            sourceChart: null,
            channelChart: null
        };

        function logResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            container.appendChild(resultDiv);
        }

        async function loadTestData() {
            logResult('data-loading-results', 'Loading test data from Airtable...', 'info');
            
            try {
                const response = await fetch('/api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblcdFVUC3zJrbmNf&maxRecords=2000');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                testData = Array.isArray(data) ? data : (data.records || []);
                
                logResult('data-loading-results', `✅ Successfully loaded ${testData.length} records`, 'success');
                
                // Update basic metrics
                updateBasicMetrics();
                
            } catch (error) {
                logResult('data-loading-results', `❌ Error loading data: ${error.message}`, 'error');
            }
        }

        function updateBasicMetrics() {
            const totalLeads = testData.length;
            const trafficSources = {};

            testData.forEach(record => {
                const source = record['Traffic Source'] || 'Other';
                trafficSources[source] = (trafficSources[source] || 0) + 1;
            });

            const googlePaid = trafficSources['Google Paid'] || 0;
            const googleOrganic = trafficSources['Google Organic'] || 0;
            const meta = trafficSources['Meta'] || 0;
            const other = totalLeads - googlePaid - googleOrganic - meta;

            document.getElementById('basic-metrics').innerHTML = `
                <div class="metric-card">
                    <div class="metric-value">${totalLeads}</div>
                    <div class="metric-label">Total Leads</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${googlePaid}</div>
                    <div class="metric-label">Google Paid</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${googleOrganic}</div>
                    <div class="metric-label">Google Organic</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${meta}</div>
                    <div class="metric-label">Meta</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${other}</div>
                    <div class="metric-label">Other Sources</div>
                </div>
            `;
        }

        // Replicate the FIXED chart data functions from your web app
        function getTimeSeriesDataFixed(data) {
            const dateSourceMap = {};
            
            data.forEach(entry => {
                const date = entry['Date Created'] ? new Date(entry['Date Created']) : null;
                if (!date) return;

                const month = date.toLocaleString('default', { month: 'short' });
                const source = entry['Traffic Source'] || 'Other';
                
                if (!dateSourceMap[month]) {
                    dateSourceMap[month] = {
                        'Google Paid': 0,
                        'Google Organic': 0,
                        'Meta': 0,
                        'Other': 0
                    };
                }

                if (source === 'Google Paid' || source === 'Google Organic' || source === 'Meta') {
                    dateSourceMap[month][source]++;
                } else {
                    dateSourceMap[month]['Other']++;
                }
            });

            const months = Object.keys(dateSourceMap);
            months.sort((a, b) => {
                const monthOrder = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
                return monthOrder.indexOf(a) - monthOrder.indexOf(b);
            });
            
            return {
                labels: months,
                datasets: [
                    {
                        label: 'Google Paid',
                        data: months.map(month => dateSourceMap[month]['Google Paid'])
                    },
                    {
                        label: 'Google Organic',
                        data: months.map(month => dateSourceMap[month]['Google Organic'])
                    },
                    {
                        label: 'Meta',
                        data: months.map(month => dateSourceMap[month]['Meta'])
                    },
                    {
                        label: 'Other',
                        data: months.map(month => dateSourceMap[month]['Other'])
                    }
                ]
            };
        }

        function getSourceDataFixed(data) {
            const sourceCounts = {
                'Google Paid': 0,
                'Google Organic': 0,
                'Meta': 0,
                'Other': 0
            };

            data.forEach(entry => {
                const source = entry['Traffic Source'] || 'Other';

                if (source === 'Google Paid' || source === 'Google Organic' || source === 'Meta') {
                    sourceCounts[source]++;
                } else {
                    sourceCounts['Other']++;
                }
            });

            return {
                labels: Object.keys(sourceCounts),
                datasets: [{
                    data: Object.values(sourceCounts)
                }]
            };
        }

        function getChannelDataFixed(data) {
            const channelCounts = {
                'Call': 0,
                'Email': 0,
                'SMS': 0,
                'FB': 0,
                'IG': 0
            };

            data.forEach(entry => {
                const channel = entry['Channel'] || '';

                if (channel === 'Call' || channel === 'Email' || channel === 'SMS' || channel === 'FB' || channel === 'IG') {
                    channelCounts[channel]++;
                }
            });

            return {
                labels: Object.keys(channelCounts),
                datasets: [{
                    data: Object.values(channelCounts)
                }]
            };
        }

        function testChartFunctions() {
            if (!testData.length) {
                logResult('chart-function-results', '❌ Please load test data first', 'error');
                return;
            }

            logResult('chart-function-results', '🔍 Testing chart data functions...', 'info');

            const timeSeriesData = getTimeSeriesDataFixed(testData);
            const sourceData = getSourceDataFixed(testData);
            const channelData = getChannelDataFixed(testData);

            // Calculate totals
            const timeSeriesTotal = timeSeriesData.datasets.reduce((sum, dataset) => 
                sum + dataset.data.reduce((dataSum, value) => dataSum + value, 0), 0);
            const sourceTotal = sourceData.datasets[0].data.reduce((sum, value) => sum + value, 0);
            const channelTotal = channelData.datasets[0].data.reduce((sum, value) => sum + value, 0);

            // Expected totals
            const expectedTotal = testData.length;
            const expectedChannelTotal = testData.filter(record => {
                const channel = record['Channel'] || '';
                return channel === 'Call' || channel === 'Email' || channel === 'SMS' || channel === 'FB' || channel === 'IG';
            }).length;

            // Store results
            testResults.volumeChart = timeSeriesTotal === expectedTotal;
            testResults.sourceChart = sourceTotal === expectedTotal;
            testResults.channelChart = channelTotal === expectedChannelTotal;

            // Display results in table
            const tbody = document.getElementById('function-comparison-tbody');
            tbody.innerHTML = '';
            
            const tests = [
                {
                    name: 'Lead Volume Chart (Time Series)',
                    expected: expectedTotal,
                    actual: timeSeriesTotal,
                    status: testResults.volumeChart,
                    details: `${timeSeriesData.labels.length} months, ${timeSeriesData.datasets.length} sources`
                },
                {
                    name: 'Source Chart (Pie)',
                    expected: expectedTotal,
                    actual: sourceTotal,
                    status: testResults.sourceChart,
                    details: `${sourceData.labels.length} sources`
                },
                {
                    name: 'Channel Chart (3D Column)',
                    expected: expectedChannelTotal,
                    actual: channelTotal,
                    status: testResults.channelChart,
                    details: `${channelData.labels.length} channels (specific channels only)`
                }
            ];

            tests.forEach(test => {
                const row = tbody.insertRow();
                row.innerHTML = `
                    <td>${test.name}</td>
                    <td>${test.expected}</td>
                    <td>${test.actual}</td>
                    <td style="color: ${test.status ? '#4caf50' : '#f44336'}">${test.status ? '✅ PASS' : '❌ FAIL'}</td>
                    <td>${test.details}</td>
                `;
            });

            document.getElementById('function-comparison-table').style.display = 'table';

            const allPassed = Object.values(testResults).every(result => result === true);
            logResult('chart-function-results',
                allPassed ? '✅ All chart functions are working correctly!' : '❌ Some chart functions have issues',
                allPassed ? 'success' : 'error'
            );
        }

        function testLeadVolumeChart() {
            if (!testData.length) {
                logResult('volume-chart-results', '❌ Please load test data first', 'error');
                return;
            }

            logResult('volume-chart-results', '🔍 Testing Lead Volume Chart (Time Series)...', 'info');

            const timeSeriesData = getTimeSeriesDataFixed(testData);
            const totalInChart = timeSeriesData.datasets.reduce((sum, dataset) =>
                sum + dataset.data.reduce((dataSum, value) => dataSum + value, 0), 0);

            logResult('volume-chart-results', `📊 Chart groups by: MONTH (${timeSeriesData.labels.length} months)`, 'info');
            logResult('volume-chart-results', `📈 Total leads in chart: ${totalInChart}`, 'info');
            logResult('volume-chart-results', `📋 Expected total: ${testData.length}`, 'info');

            const statusElement = document.getElementById('volume-chart-status');
            if (totalInChart === testData.length) {
                logResult('volume-chart-results', '✅ Lead Volume Chart totals match perfectly!', 'success');
                statusElement.className = 'fix-status fixed';
                statusElement.textContent = '✅ FIXED: Lead Volume Chart is now accurate';
            } else {
                logResult('volume-chart-results', `❌ Chart totals (${totalInChart}) don't match expected (${testData.length})`, 'error');
                statusElement.className = 'fix-status broken';
                statusElement.textContent = '❌ BROKEN: Lead Volume Chart still has issues';
            }
        }

        function testSourceChart() {
            if (!testData.length) {
                logResult('source-chart-results', '❌ Please load test data first', 'error');
                return;
            }

            logResult('source-chart-results', '🔍 Testing Source Chart (Pie Chart)...', 'info');

            const sourceData = getSourceDataFixed(testData);
            const totalInChart = sourceData.datasets[0].data.reduce((sum, value) => sum + value, 0);

            logResult('source-chart-results', `📊 Sources: ${sourceData.labels.join(', ')}`, 'info');
            logResult('source-chart-results', `📈 Total leads in chart: ${totalInChart}`, 'info');
            logResult('source-chart-results', `📋 Expected total: ${testData.length}`, 'info');

            const statusElement = document.getElementById('source-chart-status');
            if (totalInChart === testData.length) {
                logResult('source-chart-results', '✅ Source Chart totals match perfectly!', 'success');
                statusElement.className = 'fix-status fixed';
                statusElement.textContent = '✅ FIXED: Source Chart is now accurate';
            } else {
                logResult('source-chart-results', `❌ Chart totals (${totalInChart}) don't match expected (${testData.length})`, 'error');
                statusElement.className = 'fix-status broken';
                statusElement.textContent = '❌ BROKEN: Source Chart still has issues';
            }
        }

        function testChannelChart() {
            if (!testData.length) {
                logResult('channel-chart-results', '❌ Please load test data first', 'error');
                return;
            }

            logResult('channel-chart-results', '🔍 Testing Channel Chart (3D Column)...', 'info');

            const channelData = getChannelDataFixed(testData);
            const totalInChart = channelData.datasets[0].data.reduce((sum, value) => sum + value, 0);

            // Calculate expected total (only specific channels)
            const expectedTotal = testData.filter(record => {
                const channel = record['Channel'] || '';
                return channel === 'Call' || channel === 'Email' || channel === 'SMS' || channel === 'FB' || channel === 'IG';
            }).length;

            logResult('channel-chart-results', `📊 Channels: ${channelData.labels.join(', ')}`, 'info');
            logResult('channel-chart-results', `📈 Total leads in chart: ${totalInChart}`, 'info');
            logResult('channel-chart-results', `📋 Expected total (specific channels only): ${expectedTotal}`, 'info');
            logResult('channel-chart-results', `📋 Total dataset: ${testData.length} (chart only counts specific channels)`, 'info');

            const statusElement = document.getElementById('channel-chart-status');
            if (totalInChart === expectedTotal) {
                logResult('channel-chart-results', '✅ Channel Chart totals match perfectly!', 'success');
                statusElement.className = 'fix-status fixed';
                statusElement.textContent = '✅ FIXED: Channel Chart is now accurate';
            } else {
                logResult('channel-chart-results', `❌ Chart totals (${totalInChart}) don't match expected (${expectedTotal})`, 'error');
                statusElement.className = 'fix-status broken';
                statusElement.textContent = '❌ BROKEN: Channel Chart still has issues';
            }
        }

        function generateFixReport() {
            logResult('fix-report', '📋 Generating fix verification report...', 'info');

            const results = testResults;
            const totalTests = Object.keys(results).length;
            const passedTests = Object.values(results).filter(result => result === true).length;
            const failedTests = Object.values(results).filter(result => result === false).length;

            let overallStatus = 'broken';
            let statusText = 'BROKEN: Charts still have accuracy issues';
            let statusColor = '#f44336';

            if (passedTests === totalTests) {
                overallStatus = 'fixed';
                statusText = 'FIXED: All charts are now accurate!';
                statusColor = '#4caf50';
            }

            const reportHTML = `
                <div class="test-result info">
                    <strong>🔧 Fix Verification Summary:</strong><br>
                    • Total Chart Tests: ${totalTests}<br>
                    • Passed: ${passedTests}<br>
                    • Failed: ${failedTests}
                </div>

                <div class="test-result ${overallStatus === 'fixed' ? 'success' : 'error'}">
                    <strong>📈 Chart Status After Fixes:</strong><br>
                    • Lead Volume Chart: ${results.volumeChart === true ? '✅ FIXED' : results.volumeChart === false ? '❌ BROKEN' : '⏳ NOT TESTED'}<br>
                    • Source Chart: ${results.sourceChart === true ? '✅ FIXED' : results.sourceChart === false ? '❌ BROKEN' : '⏳ NOT TESTED'}<br>
                    • Channel Chart: ${results.channelChart === true ? '✅ FIXED' : results.channelChart === false ? '❌ BROKEN' : '⏳ NOT TESTED'}
                </div>

                ${overallStatus === 'fixed' ? `
                <div class="test-result success">
                    <strong>🎉 SUCCESS!</strong><br>
                    All chart accuracy issues have been resolved. The fixes to the function names and data flow are working correctly.
                    Your web app charts should now display accurate data that matches the filtered datasets.
                </div>
                ` : `
                <div class="test-result error">
                    <strong>⚠️ ISSUES REMAIN:</strong><br>
                    Some charts still have accuracy problems. Please check the individual test results above for details.
                </div>
                `}

                <div class="test-result info">
                    <strong>🔧 What Was Fixed:</strong><br>
                    • Fixed function names: initTimeRangeChart(), initSourceChart(), initChannelChart3D()<br>
                    • Fixed data flow: Charts now use window.leadFilteredDataForCharts<br>
                    • Fixed chart data updates: Charts regenerate data before re-initializing<br>
                    • Fixed channel chart logic: Only counts specific channels (Call, Email, SMS, FB, IG)
                </div>
            `;

            document.getElementById('fix-report').innerHTML = reportHTML;

            const overallStatusElement = document.getElementById('overall-fix-status');
            overallStatusElement.className = `fix-status ${overallStatus}`;
            overallStatusElement.textContent = statusText;

            logResult('fix-report', '✅ Fix verification report generated', 'success');
        }

        // Auto-load data on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                loadTestData();
            }, 1000);
        });
    </script>
</body>
</html>
