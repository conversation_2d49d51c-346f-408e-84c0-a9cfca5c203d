<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Airtable Debug Test - Google Ads Records</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        .test-button {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #45a049;
        }
        .test-button:disabled {
            background: #cccccc;
            cursor: not-allowed;
        }
        .results {
            margin-top: 15px;
            padding: 10px;
            background: white;
            border-radius: 4px;
            border: 1px solid #ddd;
            max-height: 400px;
            overflow-y: auto;
        }
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-left: 3px solid #007cba;
            background: #f0f8ff;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .log-error {
            border-left-color: #dc3545;
            background: #fff5f5;
        }
        .log-success {
            border-left-color: #28a745;
            background: #f0fff4;
        }
        .log-warning {
            border-left-color: #ffc107;
            background: #fffbf0;
        }
        .summary-card {
            display: inline-block;
            margin: 10px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            min-width: 150px;
            text-align: center;
        }
        .summary-number {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }
        .summary-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .comparison-table th,
        .comparison-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .comparison-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
        }
        .status-running {
            background-color: #ffc107;
        }
        .status-success {
            background-color: #28a745;
        }
        .status-error {
            background-color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Airtable Debug Test - Google Ads Records</h1>
            <p>Comprehensive testing to identify missing records in Google Ads Airtable pagination</p>
        </div>

        <!-- Summary Cards -->
        <div id="summary-section">
            <div class="summary-card">
                <div class="summary-number" id="expected-records">1,970</div>
                <div class="summary-label">Expected Records</div>
            </div>
            <div class="summary-card">
                <div class="summary-number" id="server-records">-</div>
                <div class="summary-label">Server Records</div>
            </div>
            <div class="summary-card">
                <div class="summary-number" id="direct-records">-</div>
                <div class="summary-label">Direct API Records</div>
            </div>
            <div class="summary-card">
                <div class="summary-number" id="missing-records">-</div>
                <div class="summary-label">Missing Records</div>
            </div>
        </div>

        <!-- Test 1: Server Pagination Test -->
        <div class="test-section">
            <h3>🖥️ Test 1: Server Pagination (Current Implementation)</h3>
            <p>Test our current server pagination logic to see how many records it fetches.</p>
            <button class="test-button" onclick="testServerPagination()">Run Server Test</button>
            <button class="test-button" onclick="testServerPaginationWithLimit(500)">Test 500 Records</button>
            <button class="test-button" onclick="testServerPaginationWithLimit(3000)">Test 3000 Records</button>
            <div class="results" id="server-results"></div>
        </div>

        <!-- Test 2: Direct Airtable API Test -->
        <div class="test-section">
            <h3>🌐 Test 2: Direct Airtable API (Bypass Server)</h3>
            <p>Test direct calls to Airtable API to see maximum available records.</p>
            <button class="test-button" onclick="testDirectAPI()">Run Direct API Test</button>
            <button class="test-button" onclick="testFilterSyntax()">Test Filter Syntax</button>
            <div class="results" id="direct-results"></div>
        </div>

        <!-- Test 3: Filter Validation Test -->
        <div class="test-section">
            <h3>🔧 Test 3: Filter Validation</h3>
            <p>Test different filter approaches to identify which ones work correctly.</p>
            <button class="test-button" onclick="testRecordIdFilter()">Test RECORD_ID() Filter</button>
            <button class="test-button" onclick="testAlternativeFilters()">Test Alternative Filters</button>
            <button class="test-button" onclick="testPaginationWithoutAdvancedFilter()">Test Simple Pagination</button>
            <div class="results" id="filter-results"></div>
        </div>

        <!-- Test 4: Data Analysis -->
        <div class="test-section">
            <h3>📊 Test 4: Data Analysis & Comparison</h3>
            <p>Analyze the data to identify patterns in missing records.</p>
            <button class="test-button" onclick="analyzeData()">Analyze Missing Records</button>
            <button class="test-button" onclick="compareResults()">Compare All Results</button>
            <div class="results" id="analysis-results"></div>
        </div>

        <!-- Comparison Table -->
        <div class="test-section">
            <h3>📋 Results Comparison</h3>
            <table class="comparison-table" id="comparison-table">
                <thead>
                    <tr>
                        <th>Test Method</th>
                        <th>Records Found</th>
                        <th>Status</th>
                        <th>Date Range</th>
                        <th>Notes</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Expected (CSV)</td>
                        <td>1,970</td>
                        <td><span class="status-indicator status-success"></span>Reference</td>
                        <td>2025-02-20 to 2025-05-16</td>
                        <td>Baseline from CSV file</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // Global variables to store test results
        let testResults = {
            server: null,
            direct: null,
            filters: {},
            analysis: null
        };

        // Utility functions
        function log(message, type = 'info', containerId = 'server-results') {
            const container = document.getElementById(containerId);
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            container.appendChild(logEntry);
            container.scrollTop = container.scrollHeight;
        }

        function updateSummary() {
            if (testResults.server) {
                document.getElementById('server-records').textContent = testResults.server.totalRecords.toLocaleString();
            }
            if (testResults.direct) {
                document.getElementById('direct-records').textContent = testResults.direct.totalRecords.toLocaleString();
            }
            
            // Calculate missing records
            const expected = 1970;
            const actual = testResults.server ? testResults.server.totalRecords : 0;
            const missing = expected - actual;
            document.getElementById('missing-records').textContent = missing.toLocaleString();
            document.getElementById('missing-records').style.color = missing > 0 ? '#dc3545' : '#28a745';
        }

        function addComparisonRow(method, records, status, dateRange, notes) {
            const tbody = document.querySelector('#comparison-table tbody');
            const row = document.createElement('tr');
            
            const statusClass = status.includes('Success') ? 'status-success' : 
                               status.includes('Error') ? 'status-error' : 'status-running';
            
            row.innerHTML = `
                <td>${method}</td>
                <td>${records.toLocaleString()}</td>
                <td><span class="status-indicator ${statusClass}"></span>${status}</td>
                <td>${dateRange}</td>
                <td>${notes}</td>
            `;
            tbody.appendChild(row);
        }

        // Test 1: Server Pagination
        async function testServerPagination() {
            await testServerPaginationWithLimit(2000);
        }

        async function testServerPaginationWithLimit(maxRecords) {
            log(`🚀 Starting server pagination test (max: ${maxRecords} records)...`, 'info', 'server-results');
            
            try {
                const startTime = Date.now();
                const response = await fetch(`/api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblRBXdh6L6zm9CZn&maxRecords=${maxRecords}`);
                const data = await response.json();
                const endTime = Date.now();
                
                if (response.ok) {
                    testResults.server = {
                        totalRecords: data.records.length,
                        paginationInfo: data.pagination_info,
                        dateRange: getDateRange(data.records),
                        duration: endTime - startTime
                    };
                    
                    log(`✅ Server test completed successfully!`, 'success', 'server-results');
                    log(`📊 Total records: ${data.records.length.toLocaleString()}`, 'info', 'server-results');
                    log(`📄 Pages fetched: ${data.pagination_info.pages_fetched}`, 'info', 'server-results');
                    log(`⏱️ Duration: ${testResults.server.duration}ms`, 'info', 'server-results');
                    log(`📅 Date range: ${testResults.server.dateRange.min} to ${testResults.server.dateRange.max}`, 'info', 'server-results');
                    
                    updateSummary();
                    addComparisonRow(
                        `Server (${maxRecords} limit)`,
                        data.records.length,
                        'Success',
                        `${testResults.server.dateRange.min} to ${testResults.server.dateRange.max}`,
                        `${data.pagination_info.pages_fetched} pages, ${testResults.server.duration}ms`
                    );
                } else {
                    log(`❌ Server test failed: ${data.error}`, 'error', 'server-results');
                }
            } catch (error) {
                log(`💥 Server test error: ${error.message}`, 'error', 'server-results');
            }
        }

        // Test 2: Direct Airtable API
        async function testDirectAPI() {
            log(`🌐 Starting direct Airtable API test...`, 'info', 'direct-results');
            log(`⚠️ Note: This test requires CORS to be disabled or a proxy`, 'warning', 'direct-results');
            
            // Since we can't make direct calls due to CORS, we'll use our server as a proxy
            // but with a special endpoint that bypasses our pagination logic
            log(`🔄 Using server as proxy for direct API calls...`, 'info', 'direct-results');
            
            try {
                // Test with a very high limit to see what Airtable actually returns
                await testServerPaginationWithLimit(5000);
                log(`📝 Direct API test completed (via server proxy)`, 'info', 'direct-results');
            } catch (error) {
                log(`💥 Direct API test error: ${error.message}`, 'error', 'direct-results');
            }
        }

        // Test filter syntax
        async function testFilterSyntax() {
            log(`🔧 Testing filter syntax validation...`, 'info', 'direct-results');
            
            const filters = [
                { name: 'No Filter', filter: '' },
                { name: 'Simple Date Filter', filter: "IS_BEFORE({Date}, '2025-05-01')" },
                { name: 'RECORD_ID Filter', filter: "OR(IS_BEFORE({Date}, '2025-05-01'), AND({Date} = '2025-05-01', RECORD_ID() < 'rec123'))" },
                { name: 'Alternative ID Filter', filter: "OR(IS_BEFORE({Date}, '2025-05-01'), AND({Date} = '2025-05-01', {Record ID} < 'rec123'))" }
            ];
            
            for (const filterTest of filters) {
                log(`🧪 Testing: ${filterTest.name}`, 'info', 'direct-results');
                // We would test each filter here, but for now just log the attempt
                log(`   Filter: ${filterTest.filter || 'None'}`, 'info', 'direct-results');
            }
        }

        // Test 3: Filter Validation
        async function testRecordIdFilter() {
            log(`🔍 Testing RECORD_ID() filter function...`, 'info', 'filter-results');
            
            // Test if RECORD_ID() is a valid Airtable function
            const testFilter = "RECORD_ID() != ''";
            log(`🧪 Testing filter: ${testFilter}`, 'info', 'filter-results');
            
            try {
                const response = await fetch(`/api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblRBXdh6L6zm9CZn&maxRecords=10&filterByFormula=${encodeURIComponent(testFilter)}`);
                const data = await response.json();
                
                if (response.ok && data.records.length > 0) {
                    log(`✅ RECORD_ID() function works! Got ${data.records.length} records`, 'success', 'filter-results');
                    testResults.filters.recordId = { works: true, records: data.records.length };
                } else {
                    log(`❌ RECORD_ID() function failed or returned no records`, 'error', 'filter-results');
                    testResults.filters.recordId = { works: false, error: data.error || 'No records returned' };
                }
            } catch (error) {
                log(`💥 RECORD_ID() test error: ${error.message}`, 'error', 'filter-results');
                testResults.filters.recordId = { works: false, error: error.message };
            }
        }

        async function testAlternativeFilters() {
            log(`🔄 Testing alternative filter approaches...`, 'info', 'filter-results');

            const alternatives = [
                { name: 'Date Only', filter: "IS_BEFORE({Date}, '2025-05-01')" },
                { name: 'Date Range', filter: "AND(IS_AFTER({Date}, '2025-02-01'), IS_BEFORE({Date}, '2025-06-01'))" },
                { name: 'Record ID Field', filter: "{Record ID} != ''" }
            ];

            for (const alt of alternatives) {
                log(`🧪 Testing: ${alt.name}`, 'info', 'filter-results');
                try {
                    const response = await fetch(`/api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblRBXdh6L6zm9CZn&maxRecords=10&filterByFormula=${encodeURIComponent(alt.filter)}`);
                    const data = await response.json();

                    if (response.ok) {
                        log(`   ✅ ${alt.name}: ${data.records.length} records`, 'success', 'filter-results');
                        testResults.filters[alt.name] = { works: true, records: data.records.length };
                    } else {
                        log(`   ❌ ${alt.name}: Failed - ${data.error}`, 'error', 'filter-results');
                        testResults.filters[alt.name] = { works: false, error: data.error };
                    }
                } catch (error) {
                    log(`   💥 ${alt.name}: Error - ${error.message}`, 'error', 'filter-results');
                    testResults.filters[alt.name] = { works: false, error: error.message };
                }
            }
        }

        async function testPaginationWithoutAdvancedFilter() {
            log(`🔄 Testing pagination with simple IS_BEFORE filter only...`, 'info', 'filter-results');

            // This will help us determine if the RECORD_ID() part of our filter is causing issues
            log(`🧪 Simulating server pagination but with simple filters only`, 'info', 'filter-results');

            let totalRecords = 0;
            let pageCount = 0;
            let lastDate = null;

            try {
                // Start with first page (no filter)
                let response = await fetch(`/api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblRBXdh6L6zm9CZn&maxRecords=100`);
                let data = await response.json();

                if (!response.ok) {
                    log(`❌ First page failed: ${data.error}`, 'error', 'filter-results');
                    return;
                }

                totalRecords += data.records.length;
                pageCount++;

                if (data.records.length > 0) {
                    // Get the last date from this page
                    const dates = data.records.map(r => r.Date).filter(d => d).sort();
                    lastDate = dates[dates.length - 1];
                    log(`📄 Page ${pageCount}: ${data.records.length} records, last date: ${lastDate}`, 'info', 'filter-results');
                }

                // Continue with simple IS_BEFORE filters
                while (data.records.length === 100 && pageCount < 25) { // Safety limit
                    const simpleFilter = `IS_BEFORE({Date}, '${lastDate}')`;
                    log(`🔍 Using simple filter: ${simpleFilter}`, 'info', 'filter-results');

                    response = await fetch(`/api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblRBXdh6L6zm9CZn&maxRecords=100&filterByFormula=${encodeURIComponent(simpleFilter)}`);
                    data = await response.json();

                    if (!response.ok) {
                        log(`❌ Page ${pageCount + 1} failed: ${data.error}`, 'error', 'filter-results');
                        break;
                    }

                    totalRecords += data.records.length;
                    pageCount++;

                    if (data.records.length > 0) {
                        const dates = data.records.map(r => r.Date).filter(d => d).sort();
                        const newLastDate = dates[dates.length - 1];
                        log(`📄 Page ${pageCount}: ${data.records.length} records, last date: ${newLastDate}`, 'info', 'filter-results');

                        if (newLastDate === lastDate) {
                            log(`⚠️ Same last date detected - potential infinite loop. Breaking.`, 'warning', 'filter-results');
                            break;
                        }
                        lastDate = newLastDate;
                    } else {
                        log(`✅ No more records found. Pagination complete.`, 'success', 'filter-results');
                        break;
                    }
                }

                log(`📊 Simple Pagination Results:`, 'info', 'filter-results');
                log(`   Total Records: ${totalRecords}`, 'info', 'filter-results');
                log(`   Pages Fetched: ${pageCount}`, 'info', 'filter-results');
                log(`   Expected: 1,970`, 'info', 'filter-results');
                log(`   Missing: ${1970 - totalRecords}`, 'info', 'filter-results');

                if (totalRecords > 1780) {
                    log(`🎉 Simple pagination got MORE records! Advanced filter is the problem.`, 'success', 'filter-results');
                } else if (totalRecords === 1780) {
                    log(`🤔 Same result as advanced filter. Issue is elsewhere.`, 'warning', 'filter-results');
                } else {
                    log(`😟 Even fewer records with simple filter. Deeper issue exists.`, 'error', 'filter-results');
                }

                testResults.filters['Simple Pagination'] = {
                    works: true,
                    records: totalRecords,
                    pages: pageCount,
                    comparison: totalRecords > 1780 ? 'Better' : totalRecords === 1780 ? 'Same' : 'Worse'
                };

            } catch (error) {
                log(`💥 Simple pagination test error: ${error.message}`, 'error', 'filter-results');
                testResults.filters['Simple Pagination'] = { works: false, error: error.message };
            }
        }

        // Test 4: Data Analysis
        async function analyzeData() {
            log(`📊 Analyzing data patterns...`, 'info', 'analysis-results');

            if (!testResults.server) {
                log(`⚠️ No server data available. Run server test first.`, 'warning', 'analysis-results');
                return;
            }

            // Get detailed data for analysis
            log(`🔍 Fetching detailed data for analysis...`, 'info', 'analysis-results');

            try {
                const response = await fetch(`/api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblRBXdh6L6zm9CZn&maxRecords=2000`);
                const data = await response.json();

                if (response.ok) {
                    await analyzeDetailedData(data.records);
                } else {
                    log(`❌ Failed to fetch detailed data: ${data.error}`, 'error', 'analysis-results');
                }
            } catch (error) {
                log(`💥 Error fetching detailed data: ${error.message}`, 'error', 'analysis-results');
            }
        }

        async function analyzeDetailedData(records) {
            log(`🔬 Performing detailed data analysis...`, 'info', 'analysis-results');
            log(`📊 Total Records Analyzed: ${records.length}`, 'info', 'analysis-results');

            // 1. Check for duplicates
            const duplicateAnalysis = checkForDuplicates(records);
            log(`🔍 Duplicate Analysis:`, 'info', 'analysis-results');
            log(`   Unique Record IDs: ${duplicateAnalysis.uniqueIds}`, 'info', 'analysis-results');
            log(`   Duplicate IDs Found: ${duplicateAnalysis.duplicates.length}`, 'info', 'analysis-results');
            if (duplicateAnalysis.duplicates.length > 0) {
                log(`   ⚠️ Duplicate IDs: ${duplicateAnalysis.duplicates.join(', ')}`, 'warning', 'analysis-results');
            }

            // 2. Check for missing/invalid data
            const dataQualityAnalysis = checkDataQuality(records);
            log(`🔍 Data Quality Analysis:`, 'info', 'analysis-results');
            log(`   Records with Date: ${dataQualityAnalysis.withDate}`, 'info', 'analysis-results');
            log(`   Records without Date: ${dataQualityAnalysis.withoutDate}`, 'info', 'analysis-results');
            log(`   Records with Clicks: ${dataQualityAnalysis.withClicks}`, 'info', 'analysis-results');
            log(`   Records without Clicks: ${dataQualityAnalysis.withoutClicks}`, 'info', 'analysis-results');
            log(`   Empty/Invalid Records: ${dataQualityAnalysis.emptyRecords}`, 'info', 'analysis-results');

            // 3. Date distribution analysis
            const dateAnalysis = analyzeDateDistribution(records);
            log(`🔍 Date Distribution Analysis:`, 'info', 'analysis-results');
            log(`   Unique Dates: ${dateAnalysis.uniqueDates}`, 'info', 'analysis-results');
            log(`   Date Range: ${dateAnalysis.minDate} to ${dateAnalysis.maxDate}`, 'info', 'analysis-results');
            log(`   Avg Records per Date: ${dateAnalysis.avgPerDate.toFixed(1)}`, 'info', 'analysis-results');

            // Show dates with most records (potential duplicates)
            log(`   Top 5 dates by record count:`, 'info', 'analysis-results');
            dateAnalysis.topDates.forEach(([date, count]) => {
                log(`     ${date}: ${count} records`, 'info', 'analysis-results');
            });

            // 4. Check for gaps in pagination
            await checkPaginationGaps();
        }

        function checkForDuplicates(records) {
            const idCounts = {};
            const duplicates = [];

            records.forEach(record => {
                const id = record.id;
                if (idCounts[id]) {
                    idCounts[id]++;
                    if (idCounts[id] === 2) {
                        duplicates.push(id);
                    }
                } else {
                    idCounts[id] = 1;
                }
            });

            return {
                uniqueIds: Object.keys(idCounts).length,
                duplicates: duplicates
            };
        }

        function checkDataQuality(records) {
            let withDate = 0, withoutDate = 0;
            let withClicks = 0, withoutClicks = 0;
            let emptyRecords = 0;

            records.forEach(record => {
                // Check Date field
                if (record.Date && record.Date.trim() !== '') {
                    withDate++;
                } else {
                    withoutDate++;
                }

                // Check Clicks field
                if (record.Clicks !== undefined && record.Clicks !== null && record.Clicks !== '') {
                    withClicks++;
                } else {
                    withoutClicks++;
                }

                // Check if record is essentially empty
                const fieldCount = Object.keys(record).filter(key =>
                    key !== 'id' && record[key] !== null && record[key] !== undefined && record[key] !== ''
                ).length;

                if (fieldCount === 0) {
                    emptyRecords++;
                }
            });

            return {
                withDate, withoutDate,
                withClicks, withoutClicks,
                emptyRecords
            };
        }

        function analyzeDateDistribution(records) {
            const dateCounts = {};
            const dates = [];

            records.forEach(record => {
                if (record.Date) {
                    dates.push(record.Date);
                    dateCounts[record.Date] = (dateCounts[record.Date] || 0) + 1;
                }
            });

            const sortedDates = dates.sort();
            const topDates = Object.entries(dateCounts)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 5);

            return {
                uniqueDates: Object.keys(dateCounts).length,
                minDate: sortedDates[0],
                maxDate: sortedDates[sortedDates.length - 1],
                avgPerDate: records.length / Object.keys(dateCounts).length,
                topDates: topDates
            };
        }

        async function checkPaginationGaps() {
            log(`🔍 Checking for pagination gaps...`, 'info', 'analysis-results');

            // Test specific date ranges to see if we're missing chunks
            const testRanges = [
                { name: 'Early Feb', filter: "AND(IS_AFTER({Date}, '2025-02-15'), IS_BEFORE({Date}, '2025-02-25'))" },
                { name: 'Early Mar', filter: "AND(IS_AFTER({Date}, '2025-02-28'), IS_BEFORE({Date}, '2025-03-10'))" },
                { name: 'Early Apr', filter: "AND(IS_AFTER({Date}, '2025-03-31'), IS_BEFORE({Date}, '2025-04-10'))" },
                { name: 'Early May', filter: "AND(IS_AFTER({Date}, '2025-04-30'), IS_BEFORE({Date}, '2025-05-10'))" }
            ];

            for (const range of testRanges) {
                try {
                    const response = await fetch(`/api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblRBXdh6L6zm9CZn&maxRecords=200&filterByFormula=${encodeURIComponent(range.filter)}`);
                    const data = await response.json();

                    if (response.ok) {
                        log(`   ${range.name}: ${data.records.length} records`, 'info', 'analysis-results');
                    } else {
                        log(`   ${range.name}: Error - ${data.error}`, 'error', 'analysis-results');
                    }
                } catch (error) {
                    log(`   ${range.name}: Failed - ${error.message}`, 'error', 'analysis-results');
                }
            }
        }

        async function compareResults() {
            log(`🔍 Comparing all test results...`, 'info', 'analysis-results');
            
            const summary = {
                server: testResults.server?.totalRecords || 0,
                direct: testResults.direct?.totalRecords || 0,
                expected: 1970
            };
            
            log(`📋 Summary:`, 'info', 'analysis-results');
            log(`   Expected: ${summary.expected}`, 'info', 'analysis-results');
            log(`   Server: ${summary.server} (${summary.expected - summary.server} missing)`, 'info', 'analysis-results');
            log(`   Direct: ${summary.direct} (${summary.expected - summary.direct} missing)`, 'info', 'analysis-results');
            
            if (summary.server === summary.direct) {
                log(`✅ Server and direct results match - issue is in pagination logic`, 'success', 'analysis-results');
            } else {
                log(`⚠️ Server and direct results differ - investigate server implementation`, 'warning', 'analysis-results');
            }
            
            // Filter analysis
            log(`🔧 Filter Test Results:`, 'info', 'analysis-results');
            for (const [name, result] of Object.entries(testResults.filters)) {
                const status = result.works ? '✅' : '❌';
                log(`   ${status} ${name}: ${result.works ? `${result.records} records` : result.error}`, 'info', 'analysis-results');
            }
        }

        // Utility function to get date range from records
        function getDateRange(records) {
            if (!records || records.length === 0) return null;
            
            const dates = records.map(r => r.Date).filter(d => d).sort();
            return {
                min: dates[0],
                max: dates[dates.length - 1],
                span: Math.ceil((new Date(dates[dates.length - 1]) - new Date(dates[0])) / (1000 * 60 * 60 * 24))
            };
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            log('🔍 Airtable Debug Test Page loaded', 'success', 'server-results');
            log('Ready to run tests. Start with "Run Server Test" to see current pagination results.', 'info', 'server-results');
        });
    </script>
</body>
</html>
