<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 Final Chart Fix Test</title>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #121212;
            color: #ffffff;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: #1e1e1e;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        h1 {
            color: #e91e63;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            background: #2a2a2a;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #e91e63;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 6px;
            border-left: 4px solid;
        }
        .test-result.success {
            background: rgba(76, 175, 80, 0.1);
            border-color: #4caf50;
            color: #4caf50;
        }
        .test-result.error {
            background: rgba(244, 67, 54, 0.1);
            border-color: #f44336;
            color: #f44336;
        }
        .test-result.info {
            background: rgba(33, 150, 243, 0.1);
            border-color: #2196f3;
            color: #2196f3;
        }
        button {
            background: linear-gradient(135deg, #e91e63, #ff5722);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            margin: 10px 10px 10px 0;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
        }
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .comparison-card {
            background: #333;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .comparison-card h4 {
            color: #e91e63;
            margin-top: 0;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #4caf50;
        }
        .metric-mismatch {
            color: #f44336;
        }
        .fix-status {
            font-size: 18px;
            font-weight: bold;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            margin: 20px 0;
        }
        .fix-status.fixed {
            background: rgba(76, 175, 80, 0.2);
            color: #4caf50;
            border: 2px solid #4caf50;
        }
        .fix-status.broken {
            background: rgba(244, 67, 54, 0.2);
            color: #f44336;
            border: 2px solid #f44336;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎯 Final Chart Fix Test</h1>
        <p>This test verifies that the final chart accuracy fixes are working correctly.</p>

        <div class="test-section">
            <h2>📊 Data Loading</h2>
            <button onclick="loadTestData()">Load Test Data</button>
            <div id="data-loading-results"></div>
        </div>

        <div class="test-section">
            <h2>🔧 Chart Function Test</h2>
            <button onclick="testChartFunctions()">Test Chart Functions</button>
            <div id="chart-function-results"></div>
            <div class="comparison-grid">
                <div class="comparison-card">
                    <h4>Lead Volume Chart</h4>
                    <div class="metric-value" id="volume-total">-</div>
                    <div>Total Leads</div>
                </div>
                <div class="comparison-card">
                    <h4>Source Chart</h4>
                    <div class="metric-value" id="source-total">-</div>
                    <div>Total Leads</div>
                </div>
                <div class="comparison-card">
                    <h4>Channel Chart</h4>
                    <div class="metric-value" id="channel-total">-</div>
                    <div>Specific Channels</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>🎯 Accuracy Verification</h2>
            <button onclick="verifyAccuracy()">Verify Chart Accuracy</button>
            <div id="accuracy-results"></div>
            <div class="fix-status" id="final-status"></div>
        </div>

        <div class="test-section">
            <h2>📋 Instructions for Web App Testing</h2>
            <div class="test-result info">
                <strong>🔍 How to Test Your Web App:</strong><br>
                1. Open your main web application<br>
                2. Go to the Leads Report tab<br>
                3. Apply different date filters (Last 14 Days, Last 30 Days)<br>
                4. Watch the charts update - they should now change when you filter!<br>
                5. Verify chart totals match the filtered record counts<br>
                6. Check that SMS and other channel counts match what you see in the data
            </div>
        </div>
    </div>

    <script>
        let testData = [];

        function logResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            container.appendChild(resultDiv);
        }

        async function loadTestData() {
            logResult('data-loading-results', 'Loading test data...', 'info');
            
            try {
                const response = await fetch('/api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblcdFVUC3zJrbmNf&maxRecords=2000');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                testData = Array.isArray(data) ? data : (data.records || []);
                
                logResult('data-loading-results', `✅ Successfully loaded ${testData.length} records`, 'success');
                
            } catch (error) {
                logResult('data-loading-results', `❌ Error loading data: ${error.message}`, 'error');
            }
        }

        function testChartFunctions() {
            if (!testData.length) {
                logResult('chart-function-results', '❌ Please load test data first', 'error');
                return;
            }

            logResult('chart-function-results', '🔍 Testing chart functions with global data access...', 'info');

            // Simulate the global data access pattern used by your web app
            window.leadFilteredDataForCharts = testData;

            // Test the chart functions (they should use the global variable)
            const timeSeriesData = getTimeSeriesDataFixed();
            const sourceData = getSourceDataFixed();
            const channelData = getChannelDataFixed();

            // Calculate totals
            const volumeTotal = timeSeriesData.datasets.reduce((sum, dataset) => 
                sum + dataset.data.reduce((dataSum, value) => dataSum + value, 0), 0);
            const sourceTotal = sourceData.datasets[0].data.reduce((sum, value) => sum + value, 0);
            const channelTotal = channelData.datasets[0].data.reduce((sum, value) => sum + value, 0);

            // Expected totals
            const expectedTotal = testData.length;
            const expectedChannelTotal = testData.filter(record => {
                const channel = record['Channel'] || '';
                return channel === 'Call' || channel === 'Email' || channel === 'SMS' || channel === 'FB' || channel === 'IG';
            }).length;

            // Update display
            document.getElementById('volume-total').textContent = volumeTotal;
            document.getElementById('volume-total').className = volumeTotal === expectedTotal ? 'metric-value' : 'metric-mismatch';

            document.getElementById('source-total').textContent = sourceTotal;
            document.getElementById('source-total').className = sourceTotal === expectedTotal ? 'metric-value' : 'metric-mismatch';

            document.getElementById('channel-total').textContent = channelTotal;
            document.getElementById('channel-total').className = channelTotal === expectedChannelTotal ? 'metric-value' : 'metric-mismatch';

            // Log results
            logResult('chart-function-results', `📊 Lead Volume Chart: ${volumeTotal} (expected: ${expectedTotal})`, 
                volumeTotal === expectedTotal ? 'success' : 'error');
            logResult('chart-function-results', `📊 Source Chart: ${sourceTotal} (expected: ${expectedTotal})`, 
                sourceTotal === expectedTotal ? 'success' : 'error');
            logResult('chart-function-results', `📊 Channel Chart: ${channelTotal} (expected: ${expectedChannelTotal})`, 
                channelTotal === expectedChannelTotal ? 'success' : 'error');
        }

        function verifyAccuracy() {
            if (!testData.length) {
                logResult('accuracy-results', '❌ Please load test data first', 'error');
                return;
            }

            logResult('accuracy-results', '🎯 Verifying final chart accuracy...', 'info');

            // Set up global data
            window.leadFilteredDataForCharts = testData;

            // Test all charts
            const timeSeriesData = getTimeSeriesDataFixed();
            const sourceData = getSourceDataFixed();
            const channelData = getChannelDataFixed();

            // Calculate totals
            const volumeTotal = timeSeriesData.datasets.reduce((sum, dataset) => 
                sum + dataset.data.reduce((dataSum, value) => dataSum + value, 0), 0);
            const sourceTotal = sourceData.datasets[0].data.reduce((sum, value) => sum + value, 0);
            const channelTotal = channelData.datasets[0].data.reduce((sum, value) => sum + value, 0);

            // Expected totals
            const expectedTotal = testData.length;
            const expectedChannelTotal = testData.filter(record => {
                const channel = record['Channel'] || '';
                return channel === 'Call' || channel === 'Email' || channel === 'SMS' || channel === 'FB' || channel === 'IG';
            }).length;

            // Check accuracy
            const volumeAccurate = volumeTotal === expectedTotal;
            const sourceAccurate = sourceTotal === expectedTotal;
            const channelAccurate = channelTotal === expectedChannelTotal;

            const allAccurate = volumeAccurate && sourceAccurate && channelAccurate;

            // Update status
            const statusElement = document.getElementById('final-status');
            if (allAccurate) {
                statusElement.className = 'fix-status fixed';
                statusElement.textContent = '✅ ALL CHARTS ARE NOW ACCURATE!';
                logResult('accuracy-results', '🎉 SUCCESS! All chart accuracy issues have been resolved.', 'success');
            } else {
                statusElement.className = 'fix-status broken';
                statusElement.textContent = '❌ Some charts still have accuracy issues';
                logResult('accuracy-results', '⚠️ Some charts still have accuracy problems.', 'error');
            }

            // Detailed breakdown
            logResult('accuracy-results', `📈 Lead Volume Chart: ${volumeAccurate ? '✅ ACCURATE' : '❌ INACCURATE'}`, 
                volumeAccurate ? 'success' : 'error');
            logResult('accuracy-results', `🥧 Source Chart: ${sourceAccurate ? '✅ ACCURATE' : '❌ INACCURATE'}`, 
                sourceAccurate ? 'success' : 'error');
            logResult('accuracy-results', `📊 Channel Chart: ${channelAccurate ? '✅ ACCURATE' : '❌ INACCURATE'}`, 
                channelAccurate ? 'success' : 'error');
        }

        // Replicate the FIXED chart functions
        function getTimeSeriesDataFixed() {
            const dataToUse = window.leadFilteredDataForCharts || [];
            const dateSourceMap = {};
            
            dataToUse.forEach(entry => {
                const date = entry['Date Created'] ? new Date(entry['Date Created']) : null;
                if (!date) return;

                const month = date.toLocaleString('default', { month: 'short' });
                const source = entry['Traffic Source'] || 'Other';
                
                if (!dateSourceMap[month]) {
                    dateSourceMap[month] = {
                        'Google Paid': 0,
                        'Google Organic': 0,
                        'Meta': 0,
                        'Other': 0
                    };
                }

                if (source === 'Google Paid' || source === 'Google Organic' || source === 'Meta') {
                    dateSourceMap[month][source]++;
                } else {
                    dateSourceMap[month]['Other']++;
                }
            });

            const months = Object.keys(dateSourceMap);
            months.sort((a, b) => {
                const monthOrder = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
                return monthOrder.indexOf(a) - monthOrder.indexOf(b);
            });
            
            return {
                labels: months,
                datasets: [
                    { label: 'Google Paid', data: months.map(month => dateSourceMap[month]['Google Paid']) },
                    { label: 'Google Organic', data: months.map(month => dateSourceMap[month]['Google Organic']) },
                    { label: 'Meta', data: months.map(month => dateSourceMap[month]['Meta']) },
                    { label: 'Other', data: months.map(month => dateSourceMap[month]['Other']) }
                ]
            };
        }

        function getSourceDataFixed() {
            const dataToUse = window.leadFilteredDataForCharts || [];
            const sourceCounts = { 'Google Paid': 0, 'Google Organic': 0, 'Meta': 0, 'Other': 0 };

            dataToUse.forEach(entry => {
                const source = entry['Traffic Source'] || 'Other';
                if (source === 'Google Paid' || source === 'Google Organic' || source === 'Meta') {
                    sourceCounts[source]++;
                } else {
                    sourceCounts['Other']++;
                }
            });

            return {
                labels: Object.keys(sourceCounts),
                datasets: [{ data: Object.values(sourceCounts) }]
            };
        }

        function getChannelDataFixed() {
            const dataToUse = window.leadFilteredDataForCharts || [];
            const channelCounts = { 'Call': 0, 'Email': 0, 'SMS': 0, 'FB': 0, 'IG': 0 };

            dataToUse.forEach(entry => {
                const channel = entry['Channel'] || '';
                if (channel === 'Call' || channel === 'Email' || channel === 'SMS' || channel === 'FB' || channel === 'IG') {
                    channelCounts[channel]++;
                }
            });

            return {
                labels: Object.keys(channelCounts),
                datasets: [{ data: Object.values(channelCounts) }]
            };
        }

        // Auto-load data on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                loadTestData();
            }, 1000);
        });
    </script>
</body>
</html>
