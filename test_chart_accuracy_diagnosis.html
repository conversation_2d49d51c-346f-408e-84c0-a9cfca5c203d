<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Chart Accuracy Diagnosis</title>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #121212;
            color: #ffffff;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }
        .test-container {
            max-width: 1400px;
            margin: 0 auto;
            background: #1e1e1e;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        h1 {
            color: #e91e63;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            background: #2a2a2a;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #e91e63;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 6px;
            border-left: 4px solid;
        }
        .test-result.success {
            background: rgba(76, 175, 80, 0.1);
            border-color: #4caf50;
            color: #4caf50;
        }
        .test-result.error {
            background: rgba(244, 67, 54, 0.1);
            border-color: #f44336;
            color: #f44336;
        }
        .test-result.info {
            background: rgba(33, 150, 243, 0.1);
            border-color: #2196f3;
            color: #2196f3;
        }
        .test-result.warning {
            background: rgba(255, 193, 7, 0.1);
            border-color: #ffc107;
            color: #ffc107;
        }
        button {
            background: linear-gradient(135deg, #e91e63, #ff5722);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            margin: 10px 10px 10px 0;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 12px;
            background: #333;
        }
        .data-table th,
        .data-table td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #444;
        }
        .data-table th {
            background: #444;
            color: #e91e63;
            font-weight: 600;
        }
        .data-table tr:nth-child(even) {
            background: rgba(255, 255, 255, 0.02);
        }
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .comparison-card {
            background: #333;
            padding: 15px;
            border-radius: 8px;
        }
        .comparison-card h4 {
            color: #e91e63;
            margin-top: 0;
        }
        .metric-value {
            font-size: 18px;
            font-weight: bold;
            color: #4caf50;
        }
        .metric-mismatch {
            color: #f44336;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔍 Chart Accuracy Diagnosis</h1>
        <p>This tool diagnoses exactly what's wrong with the Channel Distribution and Lead Sources charts by comparing actual data with chart outputs.</p>

        <div class="test-section">
            <h2>📊 Data Loading & Analysis</h2>
            <button onclick="loadAndAnalyzeData()">Load & Analyze Data</button>
            <div id="data-loading-results"></div>
        </div>

        <div class="test-section">
            <h2>🔍 Raw Data Sample Analysis</h2>
            <button onclick="analyzeRawDataSample()">Analyze Raw Data Sample</button>
            <div id="raw-data-results"></div>
            <table class="data-table" id="raw-data-table" style="display: none;">
                <thead>
                    <tr>
                        <th>Record #</th>
                        <th>Traffic Source</th>
                        <th>Channel</th>
                        <th>Date Created</th>
                    </tr>
                </thead>
                <tbody id="raw-data-tbody"></tbody>
            </table>
        </div>

        <div class="test-section">
            <h2>📈 Lead Sources Chart Diagnosis</h2>
            <button onclick="diagnoseSourceChart()">Diagnose Source Chart</button>
            <div id="source-diagnosis-results"></div>
            <div class="comparison-grid">
                <div class="comparison-card">
                    <h4>Manual Count</h4>
                    <div id="manual-source-counts"></div>
                </div>
                <div class="comparison-card">
                    <h4>Chart Function Output</h4>
                    <div id="chart-source-counts"></div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>📊 Channel Distribution Chart Diagnosis</h2>
            <button onclick="diagnoseChannelChart()">Diagnose Channel Chart</button>
            <div id="channel-diagnosis-results"></div>
            <div class="comparison-grid">
                <div class="comparison-card">
                    <h4>Manual Count</h4>
                    <div id="manual-channel-counts"></div>
                </div>
                <div class="comparison-card">
                    <h4>Chart Function Output</h4>
                    <div id="chart-channel-counts"></div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>🔧 Data Quality Issues</h2>
            <button onclick="analyzeDataQuality()">Analyze Data Quality</button>
            <div id="data-quality-results"></div>
        </div>

        <div class="test-section">
            <h2>🎯 Recommendations</h2>
            <button onclick="generateRecommendations()">Generate Fix Recommendations</button>
            <div id="recommendations-results"></div>
        </div>
    </div>

    <script>
        let testData = [];
        let diagnosisResults = {};

        function logResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            container.appendChild(resultDiv);
        }

        async function loadAndAnalyzeData() {
            logResult('data-loading-results', 'Loading data from Airtable...', 'info');
            
            try {
                const response = await fetch('/api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblcdFVUC3zJrbmNf&maxRecords=2000');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                testData = Array.isArray(data) ? data : (data.records || []);
                
                logResult('data-loading-results', `✅ Successfully loaded ${testData.length} records`, 'success');
                
                // Basic analysis
                const trafficSources = new Set();
                const channels = new Set();
                
                testData.forEach(record => {
                    if (record['Traffic Source']) trafficSources.add(record['Traffic Source']);
                    if (record['Channel']) channels.add(record['Channel']);
                });
                
                logResult('data-loading-results', `📊 Found ${trafficSources.size} unique traffic sources`, 'info');
                logResult('data-loading-results', `📊 Found ${channels.size} unique channels`, 'info');
                logResult('data-loading-results', `📊 Traffic Sources: ${Array.from(trafficSources).join(', ')}`, 'info');
                logResult('data-loading-results', `📊 Channels: ${Array.from(channels).join(', ')}`, 'info');
                
            } catch (error) {
                logResult('data-loading-results', `❌ Error loading data: ${error.message}`, 'error');
            }
        }

        function analyzeRawDataSample() {
            if (!testData.length) {
                logResult('raw-data-results', '❌ Please load data first', 'error');
                return;
            }

            logResult('raw-data-results', '🔍 Analyzing raw data sample...', 'info');

            // Show first 20 records
            const sampleData = testData.slice(0, 20);
            const tbody = document.getElementById('raw-data-tbody');
            tbody.innerHTML = '';

            sampleData.forEach((record, index) => {
                const row = tbody.insertRow();
                row.innerHTML = `
                    <td>${index + 1}</td>
                    <td>${record['Traffic Source'] || 'NULL'}</td>
                    <td>${record['Channel'] || 'NULL'}</td>
                    <td>${record['Date Created'] || 'NULL'}</td>
                `;
            });

            document.getElementById('raw-data-table').style.display = 'table';
            logResult('raw-data-results', `✅ Showing first 20 records for inspection`, 'success');
        }

        function diagnoseSourceChart() {
            if (!testData.length) {
                logResult('source-diagnosis-results', '❌ Please load data first', 'error');
                return;
            }

            logResult('source-diagnosis-results', '🔍 Diagnosing Lead Sources Chart...', 'info');

            // Manual count
            const manualCounts = {
                'Google Paid': 0,
                'Google Organic': 0,
                'Meta': 0,
                'Other': 0
            };

            const allSources = {};

            testData.forEach(record => {
                const source = record['Traffic Source'] || 'NULL';
                allSources[source] = (allSources[source] || 0) + 1;

                // Manual categorization
                if (source === 'Google Paid') {
                    manualCounts['Google Paid']++;
                } else if (source === 'Google Organic') {
                    manualCounts['Google Organic']++;
                } else if (source === 'Meta') {
                    manualCounts['Meta']++;
                } else {
                    manualCounts['Other']++;
                }
            });

            // Chart function output
            const chartData = getSourceDataFixed(testData);
            const chartCounts = {};
            chartData.labels.forEach((label, index) => {
                chartCounts[label] = chartData.datasets[0].data[index];
            });

            // Display results
            document.getElementById('manual-source-counts').innerHTML = Object.entries(manualCounts)
                .map(([source, count]) => `<div>${source}: <span class="metric-value">${count}</span></div>`)
                .join('');

            document.getElementById('chart-source-counts').innerHTML = Object.entries(chartCounts)
                .map(([source, count]) => {
                    const isMatch = manualCounts[source] === count;
                    const className = isMatch ? 'metric-value' : 'metric-mismatch';
                    return `<div>${source}: <span class="${className}">${count}</span></div>`;
                })
                .join('');

            // Check for mismatches
            let hasMismatch = false;
            Object.keys(manualCounts).forEach(source => {
                if (manualCounts[source] !== chartCounts[source]) {
                    hasMismatch = true;
                    logResult('source-diagnosis-results', `❌ Mismatch in ${source}: Manual=${manualCounts[source]}, Chart=${chartCounts[source]}`, 'error');
                }
            });

            if (!hasMismatch) {
                logResult('source-diagnosis-results', '✅ Lead Sources Chart is accurate!', 'success');
            }

            // Show all unique sources found
            logResult('source-diagnosis-results', `📊 All unique traffic sources found: ${Object.keys(allSources).join(', ')}`, 'info');
            logResult('source-diagnosis-results', `📊 Source counts: ${JSON.stringify(allSources)}`, 'info');

            diagnosisResults.sourceChart = { manual: manualCounts, chart: chartCounts, hasMismatch };
        }

        function diagnoseChannelChart() {
            if (!testData.length) {
                logResult('channel-diagnosis-results', '❌ Please load data first', 'error');
                return;
            }

            logResult('channel-diagnosis-results', '🔍 Diagnosing Channel Distribution Chart...', 'info');

            // Manual count
            const manualCounts = {
                'Call': 0,
                'Email': 0,
                'SMS': 0,
                'FB': 0,
                'IG': 0
            };

            const allChannels = {};

            testData.forEach(record => {
                const channel = record['Channel'] || 'NULL';
                allChannels[channel] = (allChannels[channel] || 0) + 1;

                // Manual categorization (only count specific channels)
                if (channel === 'Call' || channel === 'Email' || channel === 'SMS' || channel === 'FB' || channel === 'IG') {
                    manualCounts[channel]++;
                }
            });

            // Chart function output
            const chartData = getChannelDataFixed(testData);
            const chartCounts = {};
            chartData.labels.forEach((label, index) => {
                chartCounts[label] = chartData.datasets[0].data[index];
            });

            // Display results
            document.getElementById('manual-channel-counts').innerHTML = Object.entries(manualCounts)
                .map(([channel, count]) => `<div>${channel}: <span class="metric-value">${count}</span></div>`)
                .join('');

            document.getElementById('chart-channel-counts').innerHTML = Object.entries(chartCounts)
                .map(([channel, count]) => {
                    const isMatch = manualCounts[channel] === count;
                    const className = isMatch ? 'metric-value' : 'metric-mismatch';
                    return `<div>${channel}: <span class="${className}">${count}</span></div>`;
                })
                .join('');

            // Check for mismatches
            let hasMismatch = false;
            Object.keys(manualCounts).forEach(channel => {
                if (manualCounts[channel] !== chartCounts[channel]) {
                    hasMismatch = true;
                    logResult('channel-diagnosis-results', `❌ Mismatch in ${channel}: Manual=${manualCounts[channel]}, Chart=${chartCounts[channel]}`, 'error');
                }
            });

            if (!hasMismatch) {
                logResult('channel-diagnosis-results', '✅ Channel Distribution Chart is accurate!', 'success');
            }

            // Show all unique channels found
            logResult('channel-diagnosis-results', `📊 All unique channels found: ${Object.keys(allChannels).join(', ')}`, 'info');
            logResult('channel-diagnosis-results', `📊 Channel counts: ${JSON.stringify(allChannels)}`, 'info');

            const totalInChart = Object.values(chartCounts).reduce((sum, count) => sum + count, 0);
            const totalManual = Object.values(manualCounts).reduce((sum, count) => sum + count, 0);
            logResult('channel-diagnosis-results', `📊 Total in chart: ${totalInChart}, Total manual: ${totalManual}, Total dataset: ${testData.length}`, 'info');

            diagnosisResults.channelChart = { manual: manualCounts, chart: chartCounts, hasMismatch, allChannels };
        }

        // Replicate the chart functions from your web app
        function getSourceDataFixed(data) {
            const sourceCounts = {
                'Google Paid': 0,
                'Google Organic': 0,
                'Meta': 0,
                'Other': 0
            };

            data.forEach(entry => {
                const source = entry['Traffic Source'] || 'Other';

                if (source === 'Google Paid' || source === 'Google Organic' || source === 'Meta') {
                    sourceCounts[source]++;
                } else {
                    sourceCounts['Other']++;
                }
            });

            return {
                labels: Object.keys(sourceCounts),
                datasets: [{
                    data: Object.values(sourceCounts)
                }]
            };
        }

        function getChannelDataFixed(data) {
            const channelCounts = {
                'Call': 0,
                'Email': 0,
                'SMS': 0,
                'FB': 0,
                'IG': 0
            };

            data.forEach(entry => {
                const channel = entry['Channel'] || '';

                if (channel === 'Call' || channel === 'Email' || channel === 'SMS' || channel === 'FB' || channel === 'IG') {
                    channelCounts[channel]++;
                }
            });

            return {
                labels: Object.keys(channelCounts),
                datasets: [{
                    data: Object.values(channelCounts)
                }]
            };
        }

        function analyzeDataQuality() {
            if (!testData.length) {
                logResult('data-quality-results', '❌ Please load data first', 'error');
                return;
            }

            logResult('data-quality-results', '🔍 Analyzing data quality issues...', 'info');

            let nullTrafficSources = 0;
            let nullChannels = 0;
            let emptyTrafficSources = 0;
            let emptyChannels = 0;
            let unexpectedSources = [];
            let unexpectedChannels = [];

            const expectedSources = ['Google Paid', 'Google Organic', 'Meta'];
            const expectedChannels = ['Call', 'Email', 'SMS', 'FB', 'IG'];

            testData.forEach(record => {
                const source = record['Traffic Source'];
                const channel = record['Channel'];

                // Check for null/undefined traffic sources
                if (source === null || source === undefined) {
                    nullTrafficSources++;
                } else if (source === '') {
                    emptyTrafficSources++;
                } else if (!expectedSources.includes(source)) {
                    if (!unexpectedSources.includes(source)) {
                        unexpectedSources.push(source);
                    }
                }

                // Check for null/undefined channels
                if (channel === null || channel === undefined) {
                    nullChannels++;
                } else if (channel === '') {
                    emptyChannels++;
                } else if (!expectedChannels.includes(channel)) {
                    if (!unexpectedChannels.includes(channel)) {
                        unexpectedChannels.push(channel);
                    }
                }
            });

            // Report findings
            logResult('data-quality-results', `📊 Traffic Source Quality:`, 'info');
            logResult('data-quality-results', `   • Null/undefined: ${nullTrafficSources}`, nullTrafficSources > 0 ? 'warning' : 'success');
            logResult('data-quality-results', `   • Empty strings: ${emptyTrafficSources}`, emptyTrafficSources > 0 ? 'warning' : 'success');
            logResult('data-quality-results', `   • Unexpected sources: ${unexpectedSources.length} (${unexpectedSources.join(', ')})`, unexpectedSources.length > 0 ? 'warning' : 'success');

            logResult('data-quality-results', `📊 Channel Quality:`, 'info');
            logResult('data-quality-results', `   • Null/undefined: ${nullChannels}`, nullChannels > 0 ? 'warning' : 'success');
            logResult('data-quality-results', `   • Empty strings: ${emptyChannels}`, emptyChannels > 0 ? 'warning' : 'success');
            logResult('data-quality-results', `   • Unexpected channels: ${unexpectedChannels.length} (${unexpectedChannels.join(', ')})`, unexpectedChannels.length > 0 ? 'warning' : 'success');

            // Store for recommendations
            diagnosisResults.dataQuality = {
                nullTrafficSources,
                nullChannels,
                emptyTrafficSources,
                emptyChannels,
                unexpectedSources,
                unexpectedChannels
            };
        }

        function generateRecommendations() {
            logResult('recommendations-results', '🎯 Generating fix recommendations...', 'info');

            if (!diagnosisResults.sourceChart || !diagnosisResults.channelChart) {
                logResult('recommendations-results', '❌ Please run chart diagnosis first', 'error');
                return;
            }

            const sourceIssues = diagnosisResults.sourceChart.hasMismatch;
            const channelIssues = diagnosisResults.channelChart.hasMismatch;

            if (!sourceIssues && !channelIssues) {
                logResult('recommendations-results', '🎉 No issues found! Both charts are accurate.', 'success');
                return;
            }

            logResult('recommendations-results', '🔧 Recommended Fixes:', 'info');

            if (sourceIssues) {
                logResult('recommendations-results', '📈 Lead Sources Chart Issues:', 'warning');

                Object.keys(diagnosisResults.sourceChart.manual).forEach(source => {
                    const manual = diagnosisResults.sourceChart.manual[source];
                    const chart = diagnosisResults.sourceChart.chart[source];

                    if (manual !== chart) {
                        logResult('recommendations-results', `   • ${source}: Expected ${manual}, got ${chart}`, 'error');
                    }
                });

                logResult('recommendations-results', '   💡 Fix: Check getSourceData() function logic', 'info');
                logResult('recommendations-results', '   💡 Verify data filtering is working correctly', 'info');
                logResult('recommendations-results', '   💡 Check if window.leadFilteredDataForCharts is being used', 'info');
            }

            if (channelIssues) {
                logResult('recommendations-results', '📊 Channel Distribution Chart Issues:', 'warning');

                Object.keys(diagnosisResults.channelChart.manual).forEach(channel => {
                    const manual = diagnosisResults.channelChart.manual[channel];
                    const chart = diagnosisResults.channelChart.chart[channel];

                    if (manual !== chart) {
                        logResult('recommendations-results', `   • ${channel}: Expected ${manual}, got ${chart}`, 'error');
                    }
                });

                logResult('recommendations-results', '   💡 Fix: Check getChannelData() function logic', 'info');
                logResult('recommendations-results', '   💡 Verify channel name matching is case-sensitive', 'info');
                logResult('recommendations-results', '   💡 Check if filtered data is being used correctly', 'info');
            }

            // Data quality recommendations
            if (diagnosisResults.dataQuality) {
                const dq = diagnosisResults.dataQuality;

                if (dq.nullTrafficSources > 0 || dq.emptyTrafficSources > 0) {
                    logResult('recommendations-results', '⚠️ Traffic Source Data Quality Issues:', 'warning');
                    logResult('recommendations-results', `   • ${dq.nullTrafficSources + dq.emptyTrafficSources} records have missing traffic sources`, 'warning');
                    logResult('recommendations-results', '   💡 These will be categorized as "Other"', 'info');
                }

                if (dq.unexpectedSources.length > 0) {
                    logResult('recommendations-results', '⚠️ Unexpected Traffic Sources Found:', 'warning');
                    dq.unexpectedSources.forEach(source => {
                        logResult('recommendations-results', `   • "${source}" - will be categorized as "Other"`, 'warning');
                    });
                }

                if (dq.nullChannels > 0 || dq.emptyChannels > 0) {
                    logResult('recommendations-results', '⚠️ Channel Data Quality Issues:', 'warning');
                    logResult('recommendations-results', `   • ${dq.nullChannels + dq.emptyChannels} records have missing channels`, 'warning');
                    logResult('recommendations-results', '   💡 These will NOT be counted in the channel chart', 'info');
                }

                if (dq.unexpectedChannels.length > 0) {
                    logResult('recommendations-results', '⚠️ Unexpected Channels Found:', 'warning');
                    dq.unexpectedChannels.forEach(channel => {
                        logResult('recommendations-results', `   • "${channel}" - will NOT be counted in chart`, 'warning');
                    });
                    logResult('recommendations-results', '   💡 Consider adding these to the chart or updating data', 'info');
                }
            }

            logResult('recommendations-results', '✅ Diagnosis complete. Use these findings to fix chart accuracy issues.', 'success');
        }

        // Auto-load data on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                loadAndAnalyzeData();
            }, 1000);
        });
    </script>
</body>
</html>
