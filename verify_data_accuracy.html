<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Accuracy Verification</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #1a1a1a;
            color: #ffffff;
        }
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .test-section {
            background-color: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #444;
        }
        .result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #2d5a2d;
            border: 1px solid #4caf50;
        }
        .error {
            background-color: #5a2d2d;
            border: 1px solid #f44336;
        }
        .warning {
            background-color: #5a5a2d;
            border: 1px solid #ffeb3b;
        }
        .info {
            background-color: #2d4a5a;
            border: 1px solid #2196f3;
        }
        button {
            background-color: #4caf50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #45a049;
        }
        .metric-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .metric-table th, .metric-table td {
            border: 1px solid #555;
            padding: 8px;
            text-align: left;
        }
        .metric-table th {
            background-color: #333;
        }
        .expected {
            color: #4caf50;
        }
        .actual {
            color: #2196f3;
        }
        .mismatch {
            color: #f44336;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔍 Google Ads Data Accuracy Verification</h1>
        <p>This page verifies that the Google Ads data shows the correct values we achieved after fixing the pagination issue.</p>

        <div class="test-section">
            <h2>📊 Expected vs Actual Values</h2>
            <p><strong>Expected Total Clicks:</strong> <span class="expected">2,021</span> (from the successful pagination fix)</p>
            <button onclick="verifyDataAccuracy()">Verify Current Data</button>
            <div id="accuracy-results"></div>
        </div>

        <div class="test-section">
            <h2>📈 Detailed Metrics Comparison</h2>
            <table class="metric-table" id="metrics-table">
                <thead>
                    <tr>
                        <th>Metric</th>
                        <th>Expected Value</th>
                        <th>Current Value</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody id="metrics-tbody">
                    <tr><td colspan="4">Click "Verify Current Data" to populate this table</td></tr>
                </tbody>
            </table>
        </div>

        <div class="test-section">
            <h2>🔍 Filter Testing</h2>
            <button onclick="testAllTimeFilter()">Test "All Time" Filter</button>
            <button onclick="testLast14DaysFilter()">Test "Last 14 Days" Filter</button>
            <div id="filter-results"></div>
        </div>

        <div class="test-section">
            <h2>📋 Data Source Verification</h2>
            <button onclick="verifyDataSource()">Check Data Source & Record Count</button>
            <div id="source-results"></div>
        </div>

        <div class="test-section">
            <h2>🎯 Final Assessment</h2>
            <div id="final-assessment"></div>
        </div>
    </div>

    <script>
        // Expected values from the successful pagination fix
        const expectedValues = {
            totalClicks: 2021,
            totalRecords: 1970,
            // Add other expected values as we discover them
        };

        function logResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            container.appendChild(resultDiv);
        }

        function verifyDataAccuracy() {
            logResult('accuracy-results', 'Starting data accuracy verification...', 'info');
            
            try {
                // Access the main page's data
                if (window.parent && window.parent.gadsData) {
                    const gadsData = window.parent.gadsData;
                    const recordCount = gadsData.length;
                    
                    logResult('accuracy-results', `Found ${recordCount} Google Ads records`, 'info');
                    
                    // Calculate total clicks
                    let totalClicks = 0;
                    let totalImpressions = 0;
                    let totalCost = 0;
                    let totalConversions = 0;
                    
                    gadsData.forEach(record => {
                        totalClicks += parseInt(record['Clicks'] || 0);
                        totalImpressions += parseInt(record['Impressions'] || 0);
                        totalCost += parseFloat(record['Cost'] || 0);
                        totalConversions += parseFloat(record['Conversions'] || 0);
                    });
                    
                    // Update metrics table
                    updateMetricsTable({
                        'Total Records': { expected: expectedValues.totalRecords, actual: recordCount },
                        'Total Clicks': { expected: expectedValues.totalClicks, actual: totalClicks },
                        'Total Impressions': { expected: 'Unknown', actual: totalImpressions },
                        'Total Cost': { expected: 'Unknown', actual: totalCost.toFixed(2) },
                        'Total Conversions': { expected: 'Unknown', actual: totalConversions }
                    });
                    
                    // Check if values match expectations
                    if (recordCount === expectedValues.totalRecords) {
                        logResult('accuracy-results', '✅ Record count matches expected value!', 'success');
                    } else {
                        logResult('accuracy-results', `❌ Record count mismatch: Expected ${expectedValues.totalRecords}, got ${recordCount}`, 'error');
                    }
                    
                    if (totalClicks === expectedValues.totalClicks) {
                        logResult('accuracy-results', '✅ Total clicks matches expected value!', 'success');
                    } else {
                        logResult('accuracy-results', `❌ Total clicks mismatch: Expected ${expectedValues.totalClicks}, got ${totalClicks}`, 'error');
                    }
                    
                    // Show sample records
                    logResult('accuracy-results', `Sample record: ${JSON.stringify(gadsData[0], null, 2)}`, 'info');
                    
                } else {
                    logResult('accuracy-results', '❌ Cannot access main page data - Test must be run from main dashboard', 'error');
                }
            } catch (error) {
                logResult('accuracy-results', `❌ Error verifying data: ${error.message}`, 'error');
            }
            
            updateFinalAssessment();
        }

        function updateMetricsTable(metrics) {
            const tbody = document.getElementById('metrics-tbody');
            tbody.innerHTML = '';
            
            for (const [metric, values] of Object.entries(metrics)) {
                const row = document.createElement('tr');
                const isMatch = values.expected === 'Unknown' || values.expected == values.actual;
                const statusClass = isMatch ? 'success' : 'mismatch';
                const statusText = isMatch ? '✅ Match' : '❌ Mismatch';
                
                row.innerHTML = `
                    <td>${metric}</td>
                    <td class="expected">${values.expected}</td>
                    <td class="actual">${values.actual}</td>
                    <td class="${statusClass}">${statusText}</td>
                `;
                tbody.appendChild(row);
            }
        }

        function testAllTimeFilter() {
            logResult('filter-results', 'Testing "All Time" filter...', 'info');
            
            try {
                if (window.parent && window.parent.applyGadsDateFilter) {
                    // Apply All Time filter
                    window.parent.applyGadsDateFilter('all');
                    
                    setTimeout(() => {
                        const filteredData = window.parent.gadsFilteredData;
                        const originalData = window.parent.gadsData;
                        
                        if (filteredData && originalData) {
                            if (filteredData.length === originalData.length) {
                                logResult('filter-results', `✅ "All Time" filter working correctly: ${filteredData.length} records`, 'success');
                                
                                // Calculate total clicks from filtered data
                                const totalClicks = filteredData.reduce((sum, record) => sum + parseInt(record['Clicks'] || 0), 0);
                                logResult('filter-results', `Total clicks with "All Time" filter: ${totalClicks}`, 'info');
                                
                                if (totalClicks === expectedValues.totalClicks) {
                                    logResult('filter-results', '✅ "All Time" filter shows correct Total Clicks!', 'success');
                                } else {
                                    logResult('filter-results', `❌ "All Time" filter Total Clicks mismatch: Expected ${expectedValues.totalClicks}, got ${totalClicks}`, 'error');
                                }
                            } else {
                                logResult('filter-results', `❌ "All Time" filter not working: ${filteredData.length} vs ${originalData.length} records`, 'error');
                            }
                        } else {
                            logResult('filter-results', '❌ Cannot access filtered data', 'error');
                        }
                    }, 500);
                } else {
                    logResult('filter-results', '❌ Filter function not accessible', 'error');
                }
            } catch (error) {
                logResult('filter-results', `❌ Error testing filter: ${error.message}`, 'error');
            }
        }

        function testLast14DaysFilter() {
            logResult('filter-results', 'Testing "Last 14 Days" filter...', 'info');
            
            try {
                if (window.parent && window.parent.applyGadsDateFilter) {
                    // Apply Last 14 Days filter
                    window.parent.applyGadsDateFilter('last-14');
                    
                    setTimeout(() => {
                        const filteredData = window.parent.gadsFilteredData;
                        const originalData = window.parent.gadsData;
                        
                        if (filteredData && originalData) {
                            logResult('filter-results', `"Last 14 Days" filter result: ${filteredData.length} records (from ${originalData.length} total)`, 'info');
                            
                            if (filteredData.length < originalData.length && filteredData.length > 0) {
                                logResult('filter-results', '✅ "Last 14 Days" filter working correctly', 'success');
                                
                                // Calculate total clicks from filtered data
                                const totalClicks = filteredData.reduce((sum, record) => sum + parseInt(record['Clicks'] || 0), 0);
                                logResult('filter-results', `Total clicks with "Last 14 Days" filter: ${totalClicks}`, 'info');
                            } else {
                                logResult('filter-results', '❌ "Last 14 Days" filter not working correctly', 'error');
                            }
                        } else {
                            logResult('filter-results', '❌ Cannot access filtered data', 'error');
                        }
                    }, 500);
                } else {
                    logResult('filter-results', '❌ Filter function not accessible', 'error');
                }
            } catch (error) {
                logResult('filter-results', `❌ Error testing filter: ${error.message}`, 'error');
            }
        }

        function verifyDataSource() {
            logResult('source-results', 'Verifying data source...', 'info');
            
            try {
                if (window.parent) {
                    // Check if using Airtable service
                    if (window.parent.airtableService) {
                        logResult('source-results', '✅ Using Airtable service (correct)', 'success');
                    } else {
                        logResult('source-results', '❌ Airtable service not found', 'error');
                    }
                    
                    // Check global data variables
                    const gadsData = window.parent.gadsData;
                    const gadsFilteredData = window.parent.gadsFilteredData;
                    
                    if (gadsData) {
                        logResult('source-results', `✅ Global gadsData found: ${gadsData.length} records`, 'success');
                        
                        // Check data structure
                        if (gadsData.length > 0) {
                            const sampleRecord = gadsData[0];
                            const hasRequiredFields = sampleRecord['Clicks'] !== undefined && sampleRecord['Date'] !== undefined;
                            
                            if (hasRequiredFields) {
                                logResult('source-results', '✅ Data structure looks correct', 'success');
                            } else {
                                logResult('source-results', '❌ Data structure missing required fields', 'error');
                            }
                            
                            logResult('source-results', `Sample record fields: ${Object.keys(sampleRecord).join(', ')}`, 'info');
                        }
                    } else {
                        logResult('source-results', '❌ Global gadsData not found', 'error');
                    }
                    
                    if (gadsFilteredData) {
                        logResult('source-results', `✅ Filtered data available: ${gadsFilteredData.length} records`, 'success');
                    } else {
                        logResult('source-results', '⚠️ No filtered data (may be normal if no filter applied)', 'warning');
                    }
                } else {
                    logResult('source-results', '❌ Cannot access parent window', 'error');
                }
            } catch (error) {
                logResult('source-results', `❌ Error verifying data source: ${error.message}`, 'error');
            }
        }

        function updateFinalAssessment() {
            const container = document.getElementById('final-assessment');
            
            // This will be updated based on test results
            container.innerHTML = `
                <div class="result info">
                    <strong>Assessment Status:</strong> Run the verification tests above to get a complete assessment.
                </div>
                <div class="result info">
                    <strong>Key Questions:</strong>
                    <ul>
                        <li>Does the record count match 1,970?</li>
                        <li>Does Total Clicks equal 2,021?</li>
                        <li>Do the filters work correctly?</li>
                        <li>Is the data source using Airtable (not CSV)?</li>
                    </ul>
                </div>
            `;
        }

        // Initialize
        updateFinalAssessment();
    </script>
</body>
</html>
